## Hướng dẫn triển khai

- <PERSON>êu cầu hỗ trợ php7.4 trở lên
- Clone project về (<PERSON>ó thể download file zip hoặc sử dụng git clone).
- Mở file .env tiến thành chỉnh sửa các biến môi trường như: 
  - APP_ENV => production
  - APP_DEBUG => false
  - APP_URL => https://domain.xxx
- Cấu hình kết nối với csdl mariadb hoặc mysql:
  - DB_HOST => Host address
  - DB_PORT => Port kết nối (Mặc định 3306)
  - DB_DATABASE => Tên csdl
  - DB_USERNAME => Tên đăng nhập
  - DB_PASSWORD => Mật khẩu
- Import csdl mẫu sử dụng file: ./tlct_rank.sql
- Chạy task schedule "php artisan schedule:run" với tuần suất 1 tiếng / 1 lần
- <PERSON><PERSON><PERSON> ý "php" trong lệnh trên là path đến php cli

