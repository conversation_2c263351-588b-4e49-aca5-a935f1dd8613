:root {
    --bs-blue: #0d6efd;
    --bs-indigo: #6610f2;
    --bs-purple: #6f42c1;
    --bs-pink: #d63384;
    --bs-red: #dc3545;
    --bs-orange: #fd7e14;
    --bs-yellow: #ffc107;
    --bs-green: #198754;
    --bs-teal: #20c997;
    --bs-cyan: #0dcaf0;
    --bs-white: #fff;
    --bs-gray: #6c757d;
    --bs-gray-dark: #343a40;
    --bs-primary: #20446c;
    --bs-secondary: #6c757d;
    --bs-success: #198754;
    --bs-info: #0dcaf0;
    --bs-warning: #ffc107;
    --bs-danger: #dc3545;
    --bs-light: #f8f9fa;
    --bs-dark: #212529;
    --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --bs-gradient: linear-gradient(180deg, hsla(0, 0%, 100%, 0.15), hsla(0, 0%, 100%, 0))
}

*,
:after,
:before {
    box-sizing: border-box
}

body {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    background-color: #fff;
    color: #212529;
    font-family: Roboto, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    margin: 0
}

hr {
    background-color: currentColor;
    border: 0;
    color: inherit;
    margin: 1rem 0;
    opacity: .25
}

hr:not([size]) {
    height: 1px
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: .5rem;
    margin-top: 0
}

h1 {
    font-size: 2.5rem
}

h2 {
    font-size: 2rem
}

h3 {
    font-size: 1.75rem
}

h4 {
    font-size: 1.5rem
}

h5 {
    font-size: 1.25rem
}

h6 {
    font-size: 1rem
}

p {
    margin-bottom: 1rem;
    margin-top: 0
}

abbr[data-bs-original-title],
abbr[title] {
    cursor: help;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
    -webkit-text-decoration-skip-ink: none;
    text-decoration-skip-ink: none
}

address {
    font-style: normal;
    line-height: inherit;
    margin-bottom: 1rem
}

ol,
ul {
    padding-left: 2rem
}

dl,
ol,
ul {
    margin-bottom: 1rem;
    margin-top: 0
}

ol ol,
ol ul,
ul ol,
ul ul {
    margin-bottom: 0
}

dt {
    font-weight: 700
}

dd {
    margin-bottom: .5rem;
    margin-left: 0
}

blockquote {
    margin: 0 0 1rem
}

b,
strong {
    font-weight: bolder
}

small {
    font-size: .875em
}

mark {
    background-color: #fcf8e3;
    padding: .2em
}

sub,
sup {
    font-size: .75em;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

a {
    color: #20446c;
    text-decoration: none
}

a:hover {
    color: #1a3656
}

a:not([href]):not([class]),
a:not([href]):not([class]):hover {
    color: inherit;
    text-decoration: none
}

code,
kbd,
pre,
samp {
    direction: ltr;
    font-family: var(--bs-font-monospace);
    font-size: 1em;
    unicode-bidi: bidi-override
}

pre {
    display: block;
    font-size: .875em;
    margin-bottom: 1rem;
    margin-top: 0;
    overflow: auto
}

pre code {
    color: inherit;
    font-size: inherit;
    word-break: normal
}

code {
    word-wrap: break-word;
    color: #d63384;
    font-size: .875em
}

a>code {
    color: inherit
}

kbd {
    background-color: #212529;
    border-radius: .2rem;
    color: #fff;
    font-size: .875em;
    padding: .2rem .4rem
}

kbd kbd {
    font-size: 1em;
    font-weight: 700;
    padding: 0
}

figure {
    margin: 0 0 1rem
}

img,
svg {
    vertical-align: middle
}

table {
    border-collapse: collapse;
    caption-side: bottom
}

caption {
    color: #6c757d;
    padding-bottom: .5rem;
    padding-top: .5rem;
    text-align: left
}

th {
    text-align: inherit;
    text-align: -webkit-match-parent
}

tbody,
td,
tfoot,
th,
thead,
tr {
    border: 0 solid;
    border-color: inherit
}

label {
    display: inline-block
}

button {
    border-radius: 0
}

button:focus:not(:focus-visible) {
    outline: 0
}

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0
}

button,
select {
    text-transform: none
}

[role=button] {
    cursor: pointer
}

select {
    word-wrap: normal
}

select:disabled {
    opacity: 1
}

[list]::-webkit-calendar-picker-indicator {
    display: none
}

[type=button],
[type=reset],
[type=submit],
button {
    -webkit-appearance: button
}

[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled),
button:not(:disabled) {
    cursor: pointer
}

::-moz-focus-inner {
    border-style: none;
    padding: 0
}

textarea {
    resize: vertical
}

fieldset {
    border: 0;
    margin: 0;
    min-width: 0;
    padding: 0
}

legend {
    float: left;
    font-size: 1.5rem;
    line-height: inherit;
    margin-bottom: .5rem;
    padding: 0;
    width: 100%
}

legend+* {
    clear: left
}

::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-year-field {
    padding: 0
}

::-webkit-inner-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-color-swatch-wrapper {
    padding: 0
}

::file-selector-button {
    font: inherit
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

output {
    display: inline-block
}

iframe {
    border: 0
}

summary {
    cursor: pointer;
    display: list-item
}

progress {
    vertical-align: baseline
}

[hidden] {
    display: none !important
}

.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl {
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--bs-gutter-x, .75rem);
    padding-right: var(--bs-gutter-x, .75rem);
    width: 100%
}

@media (min-width:576px) {

    .container,
    .container-sm {
        max-width: 540px
    }
}

@media (min-width:768px) {

    .container,
    .container-md,
    .container-sm {
        max-width: 720px
    }
}

@media (min-width:992px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm {
        max-width: 960px
    }
}

@media (min-width:1260px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl {
        max-width: 1200px
    }
}

.row {
    --bs-gutter-x: 1.5rem;
    --bs-gutter-y: 0;
    display: flex;
    flex-wrap: wrap;
    margin-left: calc(var(--bs-gutter-x)/-2);
    margin-right: calc(var(--bs-gutter-x)/-2);
    margin-top: calc(var(--bs-gutter-y)*-1)
}

.row>* {
    flex-shrink: 0;
    margin-top: var(--bs-gutter-y);
    max-width: 100%;
    padding-left: calc(var(--bs-gutter-x)/2);
    padding-right: calc(var(--bs-gutter-x)/2);
    width: 100%
}

.col {
    flex: 1 0 0%
}

.row-cols-auto>* {
    flex: 0 0 auto;
    width: auto
}

.row-cols-1>* {
    flex: 0 0 auto;
    width: 100%
}

.row-cols-2>* {
    flex: 0 0 auto;
    width: 50%
}

.row-cols-3>* {
    flex: 0 0 auto;
    width: 33.33333%
}

.row-cols-4>* {
    flex: 0 0 auto;
    width: 25%
}

.row-cols-5>* {
    flex: 0 0 auto;
    width: 20%
}

.row-cols-6>* {
    flex: 0 0 auto;
    width: 16.66667%
}

.col-auto {
    flex: 0 0 auto;
    width: auto
}

.col-1 {
    flex: 0 0 auto;
    width: 8.33333%
}

.col-2 {
    flex: 0 0 auto;
    width: 16.66667%
}

.col-3 {
    flex: 0 0 auto;
    width: 25%
}

.col-4 {
    flex: 0 0 auto;
    width: 33.33333%
}

.col-5 {
    flex: 0 0 auto;
    width: 41.66667%
}

.col-6 {
    flex: 0 0 auto;
    width: 50%
}

.col-7 {
    flex: 0 0 auto;
    width: 58.33333%
}

.col-8 {
    flex: 0 0 auto;
    width: 66.66667%
}

.col-9 {
    flex: 0 0 auto;
    width: 75%
}

.col-10 {
    flex: 0 0 auto;
    width: 83.33333%
}

.col-11 {
    flex: 0 0 auto;
    width: 91.66667%
}

.col-12 {
    flex: 0 0 auto;
    width: 100%
}

.offset-1 {
    margin-left: 8.33333%
}

.offset-2 {
    margin-left: 16.66667%
}

.offset-3 {
    margin-left: 25%
}

.offset-4 {
    margin-left: 33.33333%
}

.offset-5 {
    margin-left: 41.66667%
}

.offset-6 {
    margin-left: 50%
}

.offset-7 {
    margin-left: 58.33333%
}

.offset-8 {
    margin-left: 66.66667%
}

.offset-9 {
    margin-left: 75%
}

.offset-10 {
    margin-left: 83.33333%
}

.offset-11 {
    margin-left: 91.66667%
}

.g-0,
.gx-0 {
    --bs-gutter-x: 0
}

.g-0,
.gy-0 {
    --bs-gutter-y: 0
}

.g-1,
.gx-1 {
    --bs-gutter-x: 0.25rem
}

.g-1,
.gy-1 {
    --bs-gutter-y: 0.25rem
}

.g-2,
.gx-2 {
    --bs-gutter-x: 0.5rem
}

.g-2,
.gy-2 {
    --bs-gutter-y: 0.5rem
}

.g-3,
.gx-3 {
    --bs-gutter-x: 1rem
}

.g-3,
.gy-3 {
    --bs-gutter-y: 1rem
}

.g-4,
.gx-4 {
    --bs-gutter-x: 1.5rem
}

.g-4,
.gy-4 {
    --bs-gutter-y: 1.5rem
}

.g-5,
.gx-5 {
    --bs-gutter-x: 3rem
}

.g-5,
.gy-5 {
    --bs-gutter-y: 3rem
}

@media (min-width:576px) {
    .col-sm {
        flex: 1 0 0%
    }

    .row-cols-sm-auto>* {
        flex: 0 0 auto;
        width: auto
    }

    .row-cols-sm-1>* {
        flex: 0 0 auto;
        width: 100%
    }

    .row-cols-sm-2>* {
        flex: 0 0 auto;
        width: 50%
    }

    .row-cols-sm-3>* {
        flex: 0 0 auto;
        width: 33.33333%
    }

    .row-cols-sm-4>* {
        flex: 0 0 auto;
        width: 25%
    }

    .row-cols-sm-5>* {
        flex: 0 0 auto;
        width: 20%
    }

    .row-cols-sm-6>* {
        flex: 0 0 auto;
        width: 16.66667%
    }

    .col-sm-auto {
        flex: 0 0 auto;
        width: auto
    }

    .col-sm-1 {
        flex: 0 0 auto;
        width: 8.33333%
    }

    .col-sm-2 {
        flex: 0 0 auto;
        width: 16.66667%
    }

    .col-sm-3 {
        flex: 0 0 auto;
        width: 25%
    }

    .col-sm-4 {
        flex: 0 0 auto;
        width: 33.33333%
    }

    .col-sm-5 {
        flex: 0 0 auto;
        width: 41.66667%
    }

    .col-sm-6 {
        flex: 0 0 auto;
        width: 50%
    }

    .col-sm-7 {
        flex: 0 0 auto;
        width: 58.33333%
    }

    .col-sm-8 {
        flex: 0 0 auto;
        width: 66.66667%
    }

    .col-sm-9 {
        flex: 0 0 auto;
        width: 75%
    }

    .col-sm-10 {
        flex: 0 0 auto;
        width: 83.33333%
    }

    .col-sm-11 {
        flex: 0 0 auto;
        width: 91.66667%
    }

    .col-sm-12 {
        flex: 0 0 auto;
        width: 100%
    }

    .offset-sm-0 {
        margin-left: 0
    }

    .offset-sm-1 {
        margin-left: 8.33333%
    }

    .offset-sm-2 {
        margin-left: 16.66667%
    }

    .offset-sm-3 {
        margin-left: 25%
    }

    .offset-sm-4 {
        margin-left: 33.33333%
    }

    .offset-sm-5 {
        margin-left: 41.66667%
    }

    .offset-sm-6 {
        margin-left: 50%
    }

    .offset-sm-7 {
        margin-left: 58.33333%
    }

    .offset-sm-8 {
        margin-left: 66.66667%
    }

    .offset-sm-9 {
        margin-left: 75%
    }

    .offset-sm-10 {
        margin-left: 83.33333%
    }

    .offset-sm-11 {
        margin-left: 91.66667%
    }

    .g-sm-0,
    .gx-sm-0 {
        --bs-gutter-x: 0
    }

    .g-sm-0,
    .gy-sm-0 {
        --bs-gutter-y: 0
    }

    .g-sm-1,
    .gx-sm-1 {
        --bs-gutter-x: 0.25rem
    }

    .g-sm-1,
    .gy-sm-1 {
        --bs-gutter-y: 0.25rem
    }

    .g-sm-2,
    .gx-sm-2 {
        --bs-gutter-x: 0.5rem
    }

    .g-sm-2,
    .gy-sm-2 {
        --bs-gutter-y: 0.5rem
    }

    .g-sm-3,
    .gx-sm-3 {
        --bs-gutter-x: 1rem
    }

    .g-sm-3,
    .gy-sm-3 {
        --bs-gutter-y: 1rem
    }

    .g-sm-4,
    .gx-sm-4 {
        --bs-gutter-x: 1.5rem
    }

    .g-sm-4,
    .gy-sm-4 {
        --bs-gutter-y: 1.5rem
    }

    .g-sm-5,
    .gx-sm-5 {
        --bs-gutter-x: 3rem
    }

    .g-sm-5,
    .gy-sm-5 {
        --bs-gutter-y: 3rem
    }
}

@media (min-width:768px) {
    .col-md {
        flex: 1 0 0%
    }

    .row-cols-md-auto>* {
        flex: 0 0 auto;
        width: auto
    }

    .row-cols-md-1>* {
        flex: 0 0 auto;
        width: 100%
    }

    .row-cols-md-2>* {
        flex: 0 0 auto;
        width: 50%
    }

    .row-cols-md-3>* {
        flex: 0 0 auto;
        width: 33.33333%
    }

    .row-cols-md-4>* {
        flex: 0 0 auto;
        width: 25%
    }

    .row-cols-md-5>* {
        flex: 0 0 auto;
        width: 20%
    }

    .row-cols-md-6>* {
        flex: 0 0 auto;
        width: 16.66667%
    }

    .col-md-auto {
        flex: 0 0 auto;
        width: auto
    }

    .col-md-1 {
        flex: 0 0 auto;
        width: 8.33333%
    }

    .col-md-2 {
        flex: 0 0 auto;
        width: 16.66667%
    }

    .col-md-3 {
        flex: 0 0 auto;
        width: 25%
    }

    .col-md-4 {
        flex: 0 0 auto;
        width: 33.33333%
    }

    .col-md-5 {
        flex: 0 0 auto;
        width: 41.66667%
    }

    .col-md-6 {
        flex: 0 0 auto;
        width: 50%
    }

    .col-md-7 {
        flex: 0 0 auto;
        width: 58.33333%
    }

    .col-md-8 {
        flex: 0 0 auto;
        width: 66.66667%
    }

    .col-md-9 {
        flex: 0 0 auto;
        width: 75%
    }

    .col-md-10 {
        flex: 0 0 auto;
        width: 83.33333%
    }

    .col-md-11 {
        flex: 0 0 auto;
        width: 91.66667%
    }

    .col-md-12 {
        flex: 0 0 auto;
        width: 100%
    }

    .offset-md-0 {
        margin-left: 0
    }

    .offset-md-1 {
        margin-left: 8.33333%
    }

    .offset-md-2 {
        margin-left: 16.66667%
    }

    .offset-md-3 {
        margin-left: 25%
    }

    .offset-md-4 {
        margin-left: 33.33333%
    }

    .offset-md-5 {
        margin-left: 41.66667%
    }

    .offset-md-6 {
        margin-left: 50%
    }

    .offset-md-7 {
        margin-left: 58.33333%
    }

    .offset-md-8 {
        margin-left: 66.66667%
    }

    .offset-md-9 {
        margin-left: 75%
    }

    .offset-md-10 {
        margin-left: 83.33333%
    }

    .offset-md-11 {
        margin-left: 91.66667%
    }

    .g-md-0,
    .gx-md-0 {
        --bs-gutter-x: 0
    }

    .g-md-0,
    .gy-md-0 {
        --bs-gutter-y: 0
    }

    .g-md-1,
    .gx-md-1 {
        --bs-gutter-x: 0.25rem
    }

    .g-md-1,
    .gy-md-1 {
        --bs-gutter-y: 0.25rem
    }

    .g-md-2,
    .gx-md-2 {
        --bs-gutter-x: 0.5rem
    }

    .g-md-2,
    .gy-md-2 {
        --bs-gutter-y: 0.5rem
    }

    .g-md-3,
    .gx-md-3 {
        --bs-gutter-x: 1rem
    }

    .g-md-3,
    .gy-md-3 {
        --bs-gutter-y: 1rem
    }

    .g-md-4,
    .gx-md-4 {
        --bs-gutter-x: 1.5rem
    }

    .g-md-4,
    .gy-md-4 {
        --bs-gutter-y: 1.5rem
    }

    .g-md-5,
    .gx-md-5 {
        --bs-gutter-x: 3rem
    }

    .g-md-5,
    .gy-md-5 {
        --bs-gutter-y: 3rem
    }
}

@media (min-width:992px) {
    .col-lg {
        flex: 1 0 0%
    }

    .row-cols-lg-auto>* {
        flex: 0 0 auto;
        width: auto
    }

    .row-cols-lg-1>* {
        flex: 0 0 auto;
        width: 100%
    }

    .row-cols-lg-2>* {
        flex: 0 0 auto;
        width: 50%
    }

    .row-cols-lg-3>* {
        flex: 0 0 auto;
        width: 33.33333%
    }

    .row-cols-lg-4>* {
        flex: 0 0 auto;
        width: 25%
    }

    .row-cols-lg-5>* {
        flex: 0 0 auto;
        width: 20%
    }

    .row-cols-lg-6>* {
        flex: 0 0 auto;
        width: 16.66667%
    }

    .col-lg-auto {
        flex: 0 0 auto;
        width: auto
    }

    .col-lg-1 {
        flex: 0 0 auto;
        width: 8.33333%
    }

    .col-lg-2 {
        flex: 0 0 auto;
        width: 16.66667%
    }

    .col-lg-3 {
        flex: 0 0 auto;
        width: 25%
    }

    .col-lg-4 {
        flex: 0 0 auto;
        width: 33.33333%
    }

    .col-lg-5 {
        flex: 0 0 auto;
        width: 41.66667%
    }

    .col-lg-6 {
        flex: 0 0 auto;
        width: 50%
    }

    .col-lg-7 {
        flex: 0 0 auto;
        width: 58.33333%
    }

    .col-lg-8 {
        flex: 0 0 auto;
        width: 66.66667%
    }

    .col-lg-9 {
        flex: 0 0 auto;
        width: 75%
    }

    .col-lg-10 {
        flex: 0 0 auto;
        width: 83.33333%
    }

    .col-lg-11 {
        flex: 0 0 auto;
        width: 91.66667%
    }

    .col-lg-12 {
        flex: 0 0 auto;
        width: 100%
    }

    .offset-lg-0 {
        margin-left: 0
    }

    .offset-lg-1 {
        margin-left: 8.33333%
    }

    .offset-lg-2 {
        margin-left: 16.66667%
    }

    .offset-lg-3 {
        margin-left: 25%
    }

    .offset-lg-4 {
        margin-left: 33.33333%
    }

    .offset-lg-5 {
        margin-left: 41.66667%
    }

    .offset-lg-6 {
        margin-left: 50%
    }

    .offset-lg-7 {
        margin-left: 58.33333%
    }

    .offset-lg-8 {
        margin-left: 66.66667%
    }

    .offset-lg-9 {
        margin-left: 75%
    }

    .offset-lg-10 {
        margin-left: 83.33333%
    }

    .offset-lg-11 {
        margin-left: 91.66667%
    }

    .g-lg-0,
    .gx-lg-0 {
        --bs-gutter-x: 0
    }

    .g-lg-0,
    .gy-lg-0 {
        --bs-gutter-y: 0
    }

    .g-lg-1,
    .gx-lg-1 {
        --bs-gutter-x: 0.25rem
    }

    .g-lg-1,
    .gy-lg-1 {
        --bs-gutter-y: 0.25rem
    }

    .g-lg-2,
    .gx-lg-2 {
        --bs-gutter-x: 0.5rem
    }

    .g-lg-2,
    .gy-lg-2 {
        --bs-gutter-y: 0.5rem
    }

    .g-lg-3,
    .gx-lg-3 {
        --bs-gutter-x: 1rem
    }

    .g-lg-3,
    .gy-lg-3 {
        --bs-gutter-y: 1rem
    }

    .g-lg-4,
    .gx-lg-4 {
        --bs-gutter-x: 1.5rem
    }

    .g-lg-4,
    .gy-lg-4 {
        --bs-gutter-y: 1.5rem
    }

    .g-lg-5,
    .gx-lg-5 {
        --bs-gutter-x: 3rem
    }

    .g-lg-5,
    .gy-lg-5 {
        --bs-gutter-y: 3rem
    }
}

@media (min-width:1260px) {
    .col-xl {
        flex: 1 0 0%
    }

    .row-cols-xl-auto>* {
        flex: 0 0 auto;
        width: auto
    }

    .row-cols-xl-1>* {
        flex: 0 0 auto;
        width: 100%
    }

    .row-cols-xl-2>* {
        flex: 0 0 auto;
        width: 50%
    }

    .row-cols-xl-3>* {
        flex: 0 0 auto;
        width: 33.33333%
    }

    .row-cols-xl-4>* {
        flex: 0 0 auto;
        width: 25%
    }

    .row-cols-xl-5>* {
        flex: 0 0 auto;
        width: 20%
    }

    .row-cols-xl-6>* {
        flex: 0 0 auto;
        width: 16.66667%
    }

    .col-xl-auto {
        flex: 0 0 auto;
        width: auto
    }

    .col-xl-1 {
        flex: 0 0 auto;
        width: 8.33333%
    }

    .col-xl-2 {
        flex: 0 0 auto;
        width: 16.66667%
    }

    .col-xl-3 {
        flex: 0 0 auto;
        width: 25%
    }

    .col-xl-4 {
        flex: 0 0 auto;
        width: 33.33333%
    }

    .col-xl-5 {
        flex: 0 0 auto;
        width: 41.66667%
    }

    .col-xl-6 {
        flex: 0 0 auto;
        width: 50%
    }

    .col-xl-7 {
        flex: 0 0 auto;
        width: 58.33333%
    }

    .col-xl-8 {
        flex: 0 0 auto;
        width: 66.66667%
    }

    .col-xl-9 {
        flex: 0 0 auto;
        width: 75%
    }

    .col-xl-10 {
        flex: 0 0 auto;
        width: 83.33333%
    }

    .col-xl-11 {
        flex: 0 0 auto;
        width: 91.66667%
    }

    .col-xl-12 {
        flex: 0 0 auto;
        width: 100%
    }

    .offset-xl-0 {
        margin-left: 0
    }

    .offset-xl-1 {
        margin-left: 8.33333%
    }

    .offset-xl-2 {
        margin-left: 16.66667%
    }

    .offset-xl-3 {
        margin-left: 25%
    }

    .offset-xl-4 {
        margin-left: 33.33333%
    }

    .offset-xl-5 {
        margin-left: 41.66667%
    }

    .offset-xl-6 {
        margin-left: 50%
    }

    .offset-xl-7 {
        margin-left: 58.33333%
    }

    .offset-xl-8 {
        margin-left: 66.66667%
    }

    .offset-xl-9 {
        margin-left: 75%
    }

    .offset-xl-10 {
        margin-left: 83.33333%
    }

    .offset-xl-11 {
        margin-left: 91.66667%
    }

    .g-xl-0,
    .gx-xl-0 {
        --bs-gutter-x: 0
    }

    .g-xl-0,
    .gy-xl-0 {
        --bs-gutter-y: 0
    }

    .g-xl-1,
    .gx-xl-1 {
        --bs-gutter-x: 0.25rem
    }

    .g-xl-1,
    .gy-xl-1 {
        --bs-gutter-y: 0.25rem
    }

    .g-xl-2,
    .gx-xl-2 {
        --bs-gutter-x: 0.5rem
    }

    .g-xl-2,
    .gy-xl-2 {
        --bs-gutter-y: 0.5rem
    }

    .g-xl-3,
    .gx-xl-3 {
        --bs-gutter-x: 1rem
    }

    .g-xl-3,
    .gy-xl-3 {
        --bs-gutter-y: 1rem
    }

    .g-xl-4,
    .gx-xl-4 {
        --bs-gutter-x: 1.5rem
    }

    .g-xl-4,
    .gy-xl-4 {
        --bs-gutter-y: 1.5rem
    }

    .g-xl-5,
    .gx-xl-5 {
        --bs-gutter-x: 3rem
    }

    .g-xl-5,
    .gy-xl-5 {
        --bs-gutter-y: 3rem
    }
}

.table {
    --bs-table-bg: transparent;
    --bs-table-accent-bg: transparent;
    --bs-table-striped-color: #212529;
    --bs-table-striped-bg: rgba(0, 0, 0, 0.05);
    --bs-table-active-color: #212529;
    --bs-table-active-bg: rgba(0, 0, 0, 0.1);
    --bs-table-hover-color: #212529;
    --bs-table-hover-bg: rgba(0, 0, 0, 0.075);
    border-color: #dee2e6;
    color: #212529;
    margin-bottom: 1rem;
    vertical-align: top;
    width: 100%
}

.table>:not(caption)>*>* {
    background-color: var(--bs-table-bg);
    border-bottom-width: 1px;
    box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
    padding: .5rem
}

.table>tbody {
    vertical-align: inherit
}

.table>thead {
    vertical-align: bottom
}

.table>:not(:last-child)>:last-child>* {
    border-bottom-color: currentColor
}

.caption-top {
    caption-side: top
}

.table-sm>:not(caption)>*>* {
    padding: .25rem
}

.table-bordered>:not(caption)>* {
    border-width: 1px 0
}

.table-bordered>:not(caption)>*>* {
    border-width: 0 1px
}

.table-borderless>:not(caption)>*>* {
    border-bottom-width: 0
}

.table-striped>tbody>tr:nth-of-type(odd) {
    --bs-table-accent-bg: var(--bs-table-striped-bg);
    color: var(--bs-table-striped-color)
}

.table-active {
    --bs-table-accent-bg: var(--bs-table-active-bg);
    color: var(--bs-table-active-color)
}

.table-hover>tbody>tr:hover {
    --bs-table-accent-bg: var(--bs-table-hover-bg);
    color: var(--bs-table-hover-color)
}

.table-primary {
    --bs-table-bg: #d2dae2;
    --bs-table-striped-bg: #c8cfd7;
    --bs-table-striped-color: #000;
    --bs-table-active-bg: #bdc4cb;
    --bs-table-active-color: #000;
    --bs-table-hover-bg: #c2cad1;
    --bs-table-hover-color: #000;
    border-color: #bdc4cb;
    color: #000
}

.table-secondary {
    --bs-table-bg: #e2e3e5;
    --bs-table-striped-bg: #d7d8da;
    --bs-table-striped-color: #000;
    --bs-table-active-bg: #cbccce;
    --bs-table-active-color: #000;
    --bs-table-hover-bg: #d1d2d4;
    --bs-table-hover-color: #000;
    border-color: #cbccce;
    color: #000
}

.table-success {
    --bs-table-bg: #d1e7dd;
    --bs-table-striped-bg: #c7dbd2;
    --bs-table-striped-color: #000;
    --bs-table-active-bg: #bcd0c7;
    --bs-table-active-color: #000;
    --bs-table-hover-bg: #c1d6cc;
    --bs-table-hover-color: #000;
    border-color: #bcd0c7;
    color: #000
}

.table-info {
    --bs-table-bg: #cff4fc;
    --bs-table-striped-bg: #c5e8ef;
    --bs-table-striped-color: #000;
    --bs-table-active-bg: #badce3;
    --bs-table-active-color: #000;
    --bs-table-hover-bg: #bfe2e9;
    --bs-table-hover-color: #000;
    border-color: #badce3;
    color: #000
}

.table-warning {
    --bs-table-bg: #fff3cd;
    --bs-table-striped-bg: #f2e7c3;
    --bs-table-striped-color: #000;
    --bs-table-active-bg: #e6dbb9;
    --bs-table-active-color: #000;
    --bs-table-hover-bg: #ece1be;
    --bs-table-hover-color: #000;
    border-color: #e6dbb9;
    color: #000
}

.table-danger {
    --bs-table-bg: #f8d7da;
    --bs-table-striped-bg: #eccccf;
    --bs-table-striped-color: #000;
    --bs-table-active-bg: #dfc2c4;
    --bs-table-active-color: #000;
    --bs-table-hover-bg: #e5c7ca;
    --bs-table-hover-color: #000;
    border-color: #dfc2c4;
    color: #000
}

.table-light {
    --bs-table-bg: #f8f9fa;
    --bs-table-striped-bg: #ecedee;
    --bs-table-striped-color: #000;
    --bs-table-active-bg: #dfe0e1;
    --bs-table-active-color: #000;
    --bs-table-hover-bg: #e5e6e7;
    --bs-table-hover-color: #000;
    border-color: #dfe0e1;
    color: #000
}

.table-dark {
    --bs-table-bg: #212529;
    --bs-table-striped-bg: #2c3034;
    --bs-table-striped-color: #fff;
    --bs-table-active-bg: #373b3e;
    --bs-table-active-color: #fff;
    --bs-table-hover-bg: #323539;
    --bs-table-hover-color: #fff;
    border-color: #373b3e;
    color: #fff
}

.table-responsive {
    -webkit-overflow-scrolling: touch;
    overflow-x: auto
}

@media (max-width:575.98px) {
    .table-responsive-sm {
        -webkit-overflow-scrolling: touch;
        overflow-x: auto
    }
}

@media (max-width:767.98px) {
    .table-responsive-md {
        -webkit-overflow-scrolling: touch;
        overflow-x: auto
    }
}

@media (max-width:991.98px) {
    .table-responsive-lg {
        -webkit-overflow-scrolling: touch;
        overflow-x: auto
    }
}

@media (max-width:1259.98px) {
    .table-responsive-xl {
        -webkit-overflow-scrolling: touch;
        overflow-x: auto
    }
}

.btn {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: .25rem;
    color: #212529;
    cursor: pointer;
    display: inline-block;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    padding: .375rem .75rem;
    text-align: center;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    vertical-align: middle
}

@media (prefers-reduced-motion:reduce) {
    .btn {
        transition: none
    }
}

.btn:hover {
    color: #212529
}

.btn-check:focus+.btn,
.btn:focus {
    box-shadow: 0 0 0 0 rgba(32, 68, 108, .25);
    outline: 0
}

.btn.disabled,
.btn:disabled,
fieldset:disabled .btn {
    opacity: .65;
    pointer-events: none
}

.btn-primary {
    background-color: #20446c;
    border-color: #20446c;
    color: #fff
}

.btn-check:focus+.btn-primary,
.btn-primary:focus,
.btn-primary:hover {
    background-color: #1b3a5c;
    border-color: #1a3656;
    color: #fff
}

.btn-check:focus+.btn-primary,
.btn-primary:focus {
    box-shadow: 0 0 0 0 rgba(65, 96, 130, .5)
}

.btn-check:active+.btn-primary,
.btn-check:checked+.btn-primary,
.btn-primary.active,
.btn-primary:active,
.show>.btn-primary.dropdown-toggle {
    background-color: #1a3656;
    border-color: #183351;
    color: #fff
}

.btn-check:active+.btn-primary:focus,
.btn-check:checked+.btn-primary:focus,
.btn-primary.active:focus,
.btn-primary:active:focus,
.show>.btn-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0 rgba(65, 96, 130, .5)
}

.btn-primary.disabled,
.btn-primary:disabled {
    background-color: #20446c;
    border-color: #20446c;
    color: #fff
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff
}

.btn-check:focus+.btn-secondary,
.btn-secondary:focus,
.btn-secondary:hover {
    background-color: #5c636a;
    border-color: #565e64;
    color: #fff
}

.btn-check:focus+.btn-secondary,
.btn-secondary:focus {
    box-shadow: 0 0 0 0 hsla(208, 6%, 54%, .5)
}

.btn-check:active+.btn-secondary,
.btn-check:checked+.btn-secondary,
.btn-secondary.active,
.btn-secondary:active,
.show>.btn-secondary.dropdown-toggle {
    background-color: #565e64;
    border-color: #51585e;
    color: #fff
}

.btn-check:active+.btn-secondary:focus,
.btn-check:checked+.btn-secondary:focus,
.btn-secondary.active:focus,
.btn-secondary:active:focus,
.show>.btn-secondary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0 hsla(208, 6%, 54%, .5)
}

.btn-secondary.disabled,
.btn-secondary:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
    color: #fff
}

.btn-check:focus+.btn-success,
.btn-success:focus,
.btn-success:hover {
    background-color: #157347;
    border-color: #146c43;
    color: #fff
}

.btn-check:focus+.btn-success,
.btn-success:focus {
    box-shadow: 0 0 0 0 rgba(60, 153, 110, .5)
}

.btn-check:active+.btn-success,
.btn-check:checked+.btn-success,
.btn-success.active,
.btn-success:active,
.show>.btn-success.dropdown-toggle {
    background-color: #146c43;
    border-color: #13653f;
    color: #fff
}

.btn-check:active+.btn-success:focus,
.btn-check:checked+.btn-success:focus,
.btn-success.active:focus,
.btn-success:active:focus,
.show>.btn-success.dropdown-toggle:focus {
    box-shadow: 0 0 0 0 rgba(60, 153, 110, .5)
}

.btn-success.disabled,
.btn-success:disabled {
    background-color: #198754;
    border-color: #198754;
    color: #fff
}

.btn-info {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
    color: #000
}

.btn-check:focus+.btn-info,
.btn-info:focus,
.btn-info:hover {
    background-color: #31d2f2;
    border-color: #25cff2;
    color: #000
}

.btn-check:focus+.btn-info,
.btn-info:focus {
    box-shadow: 0 0 0 0 rgba(11, 172, 204, .5)
}

.btn-check:active+.btn-info,
.btn-check:checked+.btn-info,
.btn-info.active,
.btn-info:active,
.show>.btn-info.dropdown-toggle {
    background-color: #3dd5f3;
    border-color: #25cff2;
    color: #000
}

.btn-check:active+.btn-info:focus,
.btn-check:checked+.btn-info:focus,
.btn-info.active:focus,
.btn-info:active:focus,
.show>.btn-info.dropdown-toggle:focus {
    box-shadow: 0 0 0 0 rgba(11, 172, 204, .5)
}

.btn-info.disabled,
.btn-info:disabled {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
    color: #000
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000
}

.btn-check:focus+.btn-warning,
.btn-warning:focus,
.btn-warning:hover {
    background-color: #ffca2c;
    border-color: #ffc720;
    color: #000
}

.btn-check:focus+.btn-warning,
.btn-warning:focus {
    box-shadow: 0 0 0 0 rgba(217, 164, 6, .5)
}

.btn-check:active+.btn-warning,
.btn-check:checked+.btn-warning,
.btn-warning.active,
.btn-warning:active,
.show>.btn-warning.dropdown-toggle {
    background-color: #ffcd39;
    border-color: #ffc720;
    color: #000
}

.btn-check:active+.btn-warning:focus,
.btn-check:checked+.btn-warning:focus,
.btn-warning.active:focus,
.btn-warning:active:focus,
.show>.btn-warning.dropdown-toggle:focus {
    box-shadow: 0 0 0 0 rgba(217, 164, 6, .5)
}

.btn-warning.disabled,
.btn-warning:disabled {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff
}

.btn-check:focus+.btn-danger,
.btn-danger:focus,
.btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #b02a37;
    color: #fff
}

.btn-check:focus+.btn-danger,
.btn-danger:focus {
    box-shadow: 0 0 0 0 rgba(225, 83, 97, .5)
}

.btn-check:active+.btn-danger,
.btn-check:checked+.btn-danger,
.btn-danger.active,
.btn-danger:active,
.show>.btn-danger.dropdown-toggle {
    background-color: #b02a37;
    border-color: #a52834;
    color: #fff
}

.btn-check:active+.btn-danger:focus,
.btn-check:checked+.btn-danger:focus,
.btn-danger.active:focus,
.btn-danger:active:focus,
.show>.btn-danger.dropdown-toggle:focus {
    box-shadow: 0 0 0 0 rgba(225, 83, 97, .5)
}

.btn-danger.disabled,
.btn-danger:disabled {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    color: #000
}

.btn-check:focus+.btn-light,
.btn-light:focus,
.btn-light:hover {
    background-color: #f9fafb;
    border-color: #f9fafb;
    color: #000
}

.btn-check:focus+.btn-light,
.btn-light:focus {
    box-shadow: 0 0 0 0 hsla(210, 2%, 83%, .5)
}

.btn-check:active+.btn-light,
.btn-check:checked+.btn-light,
.btn-light.active,
.btn-light:active,
.show>.btn-light.dropdown-toggle {
    background-color: #f9fafb;
    border-color: #f9fafb;
    color: #000
}

.btn-check:active+.btn-light:focus,
.btn-check:checked+.btn-light:focus,
.btn-light.active:focus,
.btn-light:active:focus,
.show>.btn-light.dropdown-toggle:focus {
    box-shadow: 0 0 0 0 hsla(210, 2%, 83%, .5)
}

.btn-light.disabled,
.btn-light:disabled {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    color: #000
}

.btn-dark {
    background-color: #212529;
    border-color: #212529;
    color: #fff
}

.btn-check:focus+.btn-dark,
.btn-dark:focus,
.btn-dark:hover {
    background-color: #1c1f23;
    border-color: #1a1e21;
    color: #fff
}

.btn-check:focus+.btn-dark,
.btn-dark:focus {
    box-shadow: 0 0 0 0 rgba(66, 70, 73, .5)
}

.btn-check:active+.btn-dark,
.btn-check:checked+.btn-dark,
.btn-dark.active,
.btn-dark:active,
.show>.btn-dark.dropdown-toggle {
    background-color: #1a1e21;
    border-color: #191c1f;
    color: #fff
}

.btn-check:active+.btn-dark:focus,
.btn-check:checked+.btn-dark:focus,
.btn-dark.active:focus,
.btn-dark:active:focus,
.show>.btn-dark.dropdown-toggle:focus {
    box-shadow: 0 0 0 0 rgba(66, 70, 73, .5)
}

.btn-dark.disabled,
.btn-dark:disabled {
    background-color: #212529;
    border-color: #212529;
    color: #fff
}

.btn-outline-primary {
    border-color: #20446c;
    color: #20446c
}

.btn-outline-primary:hover {
    background-color: #20446c;
    border-color: #20446c;
    color: #fff
}

.btn-check:focus+.btn-outline-primary,
.btn-outline-primary:focus {
    box-shadow: 0 0 0 0 rgba(32, 68, 108, .5)
}

.btn-check:active+.btn-outline-primary,
.btn-check:checked+.btn-outline-primary,
.btn-outline-primary.active,
.btn-outline-primary.dropdown-toggle.show,
.btn-outline-primary:active {
    background-color: #20446c;
    border-color: #20446c;
    color: #fff
}

.btn-check:active+.btn-outline-primary:focus,
.btn-check:checked+.btn-outline-primary:focus,
.btn-outline-primary.active:focus,
.btn-outline-primary.dropdown-toggle.show:focus,
.btn-outline-primary:active:focus {
    box-shadow: 0 0 0 0 rgba(32, 68, 108, .5)
}

.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
    background-color: transparent;
    color: #20446c
}

.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff
}

.btn-check:focus+.btn-outline-secondary,
.btn-outline-secondary:focus {
    box-shadow: 0 0 0 0 hsla(208, 7%, 46%, .5)
}

.btn-check:active+.btn-outline-secondary,
.btn-check:checked+.btn-outline-secondary,
.btn-outline-secondary.active,
.btn-outline-secondary.dropdown-toggle.show,
.btn-outline-secondary:active {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff
}

.btn-check:active+.btn-outline-secondary:focus,
.btn-check:checked+.btn-outline-secondary:focus,
.btn-outline-secondary.active:focus,
.btn-outline-secondary.dropdown-toggle.show:focus,
.btn-outline-secondary:active:focus {
    box-shadow: 0 0 0 0 hsla(208, 7%, 46%, .5)
}

.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
    background-color: transparent;
    color: #6c757d
}

.btn-outline-success {
    border-color: #198754;
    color: #198754
}

.btn-outline-success:hover {
    background-color: #198754;
    border-color: #198754;
    color: #fff
}

.btn-check:focus+.btn-outline-success,
.btn-outline-success:focus {
    box-shadow: 0 0 0 0 rgba(25, 135, 84, .5)
}

.btn-check:active+.btn-outline-success,
.btn-check:checked+.btn-outline-success,
.btn-outline-success.active,
.btn-outline-success.dropdown-toggle.show,
.btn-outline-success:active {
    background-color: #198754;
    border-color: #198754;
    color: #fff
}

.btn-check:active+.btn-outline-success:focus,
.btn-check:checked+.btn-outline-success:focus,
.btn-outline-success.active:focus,
.btn-outline-success.dropdown-toggle.show:focus,
.btn-outline-success:active:focus {
    box-shadow: 0 0 0 0 rgba(25, 135, 84, .5)
}

.btn-outline-success.disabled,
.btn-outline-success:disabled {
    background-color: transparent;
    color: #198754
}

.btn-outline-info {
    border-color: #0dcaf0;
    color: #0dcaf0
}

.btn-outline-info:hover {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
    color: #000
}

.btn-check:focus+.btn-outline-info,
.btn-outline-info:focus {
    box-shadow: 0 0 0 0 rgba(13, 202, 240, .5)
}

.btn-check:active+.btn-outline-info,
.btn-check:checked+.btn-outline-info,
.btn-outline-info.active,
.btn-outline-info.dropdown-toggle.show,
.btn-outline-info:active {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
    color: #000
}

.btn-check:active+.btn-outline-info:focus,
.btn-check:checked+.btn-outline-info:focus,
.btn-outline-info.active:focus,
.btn-outline-info.dropdown-toggle.show:focus,
.btn-outline-info:active:focus {
    box-shadow: 0 0 0 0 rgba(13, 202, 240, .5)
}

.btn-outline-info.disabled,
.btn-outline-info:disabled {
    background-color: transparent;
    color: #0dcaf0
}

.btn-outline-warning {
    border-color: #ffc107;
    color: #ffc107
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000
}

.btn-check:focus+.btn-outline-warning,
.btn-outline-warning:focus {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, .5)
}

.btn-check:active+.btn-outline-warning,
.btn-check:checked+.btn-outline-warning,
.btn-outline-warning.active,
.btn-outline-warning.dropdown-toggle.show,
.btn-outline-warning:active {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000
}

.btn-check:active+.btn-outline-warning:focus,
.btn-check:checked+.btn-outline-warning:focus,
.btn-outline-warning.active:focus,
.btn-outline-warning.dropdown-toggle.show:focus,
.btn-outline-warning:active:focus {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, .5)
}

.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
    background-color: transparent;
    color: #ffc107
}

.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff
}

.btn-check:focus+.btn-outline-danger,
.btn-outline-danger:focus {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, .5)
}

.btn-check:active+.btn-outline-danger,
.btn-check:checked+.btn-outline-danger,
.btn-outline-danger.active,
.btn-outline-danger.dropdown-toggle.show,
.btn-outline-danger:active {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff
}

.btn-check:active+.btn-outline-danger:focus,
.btn-check:checked+.btn-outline-danger:focus,
.btn-outline-danger.active:focus,
.btn-outline-danger.dropdown-toggle.show:focus,
.btn-outline-danger:active:focus {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, .5)
}

.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
    background-color: transparent;
    color: #dc3545
}

.btn-outline-light {
    border-color: #f8f9fa;
    color: #f8f9fa
}

.btn-outline-light:hover {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    color: #000
}

.btn-check:focus+.btn-outline-light,
.btn-outline-light:focus {
    box-shadow: 0 0 0 0 rgba(248, 249, 250, .5)
}

.btn-check:active+.btn-outline-light,
.btn-check:checked+.btn-outline-light,
.btn-outline-light.active,
.btn-outline-light.dropdown-toggle.show,
.btn-outline-light:active {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    color: #000
}

.btn-check:active+.btn-outline-light:focus,
.btn-check:checked+.btn-outline-light:focus,
.btn-outline-light.active:focus,
.btn-outline-light.dropdown-toggle.show:focus,
.btn-outline-light:active:focus {
    box-shadow: 0 0 0 0 rgba(248, 249, 250, .5)
}

.btn-outline-light.disabled,
.btn-outline-light:disabled {
    background-color: transparent;
    color: #f8f9fa
}

.btn-outline-dark {
    border-color: #212529;
    color: #212529
}

.btn-outline-dark:hover {
    background-color: #212529;
    border-color: #212529;
    color: #fff
}

.btn-check:focus+.btn-outline-dark,
.btn-outline-dark:focus {
    box-shadow: 0 0 0 0 rgba(33, 37, 41, .5)
}

.btn-check:active+.btn-outline-dark,
.btn-check:checked+.btn-outline-dark,
.btn-outline-dark.active,
.btn-outline-dark.dropdown-toggle.show,
.btn-outline-dark:active {
    background-color: #212529;
    border-color: #212529;
    color: #fff
}

.btn-check:active+.btn-outline-dark:focus,
.btn-check:checked+.btn-outline-dark:focus,
.btn-outline-dark.active:focus,
.btn-outline-dark.dropdown-toggle.show:focus,
.btn-outline-dark:active:focus {
    box-shadow: 0 0 0 0 rgba(33, 37, 41, .5)
}

.btn-outline-dark.disabled,
.btn-outline-dark:disabled {
    background-color: transparent;
    color: #212529
}

.btn-link {
    color: #20446c;
    font-weight: 400;
    text-decoration: none
}

.btn-link:hover {
    color: #1a3656
}

.btn-link.disabled,
.btn-link:disabled {
    color: #6c757d
}

.btn-lg {
    border-radius: .3rem;
    font-size: 1.25rem;
    padding: .5rem 1rem
}

.btn-sm {
    border-radius: .2rem;
    font-size: .875rem;
    padding: .25rem .5rem
}

.fade {
    transition: opacity .15s linear
}

@media (prefers-reduced-motion:reduce) {
    .fade {
        transition: none
    }
}

.fade:not(.show) {
    opacity: 0
}

.collapse:not(.show) {
    display: none
}

.collapsing {
    height: 0;
    overflow: hidden;
    transition: height .35s ease
}

@media (prefers-reduced-motion:reduce) {
    .collapsing {
        transition: none
    }
}

@-webkit-keyframes progress-bar-stripes {
    0% {
        background-position-x: 1rem
    }
}

@keyframes progress-bar-stripes {
    0% {
        background-position-x: 1rem
    }
}

.progress {
    background-color: #e9ecef;
    border-radius: .25rem;
    font-size: .75rem;
    height: 1rem
}

.progress,
.progress-bar {
    display: flex;
    overflow: hidden
}

.progress-bar {
    background-color: #20446c;
    color: #fff;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    transition: width .6s ease;
    white-space: nowrap
}

@media (prefers-reduced-motion:reduce) {
    .progress-bar {
        transition: none
    }
}

.progress-bar-striped {
    background-image: linear-gradient(45deg, hsla(0, 0%, 100%, .15) 25%, transparent 0, transparent 50%, hsla(0, 0%, 100%, .15) 0, hsla(0, 0%, 100%, .15) 75%, transparent 0, transparent);
    background-size: 1rem 1rem
}

.progress-bar-animated {
    -webkit-animation: progress-bar-stripes 1s linear infinite;
    animation: progress-bar-stripes 1s linear infinite
}

@media (prefers-reduced-motion:reduce) {
    .progress-bar-animated {
        -webkit-animation: none;
        animation: none
    }
}

.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
    clip: rect(0, 0, 0, 0) !important;
    border: 0 !important;
    height: 1px !important;
    margin: -1px !important;
    overflow: hidden !important;
    padding: 0 !important;
    position: absolute !important;
    white-space: nowrap !important;
    width: 1px !important
}

.d-inline {
    display: inline !important
}

.d-inline-block {
    display: inline-block !important
}

.d-block {
    display: block !important
}

.d-grid {
    display: grid !important
}

.d-table {
    display: table !important
}

.d-table-row {
    display: table-row !important
}

.d-table-cell {
    display: table-cell !important
}

.d-flex {
    display: flex !important
}

.d-inline-flex {
    display: inline-flex !important
}

.d-none {
    display: none !important
}

.w-25 {
    width: 25% !important
}

.w-50 {
    width: 50% !important
}

.w-75 {
    width: 75% !important
}

.w-100 {
    width: 100% !important
}

.w-auto {
    width: auto !important
}

.flex-row {
    flex-direction: row !important
}

.flex-column {
    flex-direction: column !important
}

.flex-row-reverse {
    flex-direction: row-reverse !important
}

.flex-column-reverse {
    flex-direction: column-reverse !important
}

.flex-grow-0 {
    flex-grow: 0 !important
}

.flex-grow-1 {
    flex-grow: 1 !important
}

.justify-content-start {
    justify-content: flex-start !important
}

.justify-content-end {
    justify-content: flex-end !important
}

.justify-content-center {
    justify-content: center !important
}

.justify-content-between {
    justify-content: space-between !important
}

.justify-content-around {
    justify-content: space-around !important
}

.justify-content-evenly {
    justify-content: space-evenly !important
}

@media (min-width:576px) {
    .d-sm-inline {
        display: inline !important
    }

    .d-sm-inline-block {
        display: inline-block !important
    }

    .d-sm-block {
        display: block !important
    }

    .d-sm-grid {
        display: grid !important
    }

    .d-sm-table {
        display: table !important
    }

    .d-sm-table-row {
        display: table-row !important
    }

    .d-sm-table-cell {
        display: table-cell !important
    }

    .d-sm-flex {
        display: flex !important
    }

    .d-sm-inline-flex {
        display: inline-flex !important
    }

    .d-sm-none {
        display: none !important
    }

    .flex-sm-row {
        flex-direction: row !important
    }

    .flex-sm-column {
        flex-direction: column !important
    }

    .flex-sm-row-reverse {
        flex-direction: row-reverse !important
    }

    .flex-sm-column-reverse {
        flex-direction: column-reverse !important
    }

    .flex-sm-grow-0 {
        flex-grow: 0 !important
    }

    .flex-sm-grow-1 {
        flex-grow: 1 !important
    }

    .justify-content-sm-start {
        justify-content: flex-start !important
    }

    .justify-content-sm-end {
        justify-content: flex-end !important
    }

    .justify-content-sm-center {
        justify-content: center !important
    }

    .justify-content-sm-between {
        justify-content: space-between !important
    }

    .justify-content-sm-around {
        justify-content: space-around !important
    }

    .justify-content-sm-evenly {
        justify-content: space-evenly !important
    }
}

@media (min-width:768px) {
    .d-md-inline {
        display: inline !important
    }

    .d-md-inline-block {
        display: inline-block !important
    }

    .d-md-block {
        display: block !important
    }

    .d-md-grid {
        display: grid !important
    }

    .d-md-table {
        display: table !important
    }

    .d-md-table-row {
        display: table-row !important
    }

    .d-md-table-cell {
        display: table-cell !important
    }

    .d-md-flex {
        display: flex !important
    }

    .d-md-inline-flex {
        display: inline-flex !important
    }

    .d-md-none {
        display: none !important
    }

    .flex-md-row {
        flex-direction: row !important
    }

    .flex-md-column {
        flex-direction: column !important
    }

    .flex-md-row-reverse {
        flex-direction: row-reverse !important
    }

    .flex-md-column-reverse {
        flex-direction: column-reverse !important
    }

    .flex-md-grow-0 {
        flex-grow: 0 !important
    }

    .flex-md-grow-1 {
        flex-grow: 1 !important
    }

    .justify-content-md-start {
        justify-content: flex-start !important
    }

    .justify-content-md-end {
        justify-content: flex-end !important
    }

    .justify-content-md-center {
        justify-content: center !important
    }

    .justify-content-md-between {
        justify-content: space-between !important
    }

    .justify-content-md-around {
        justify-content: space-around !important
    }

    .justify-content-md-evenly {
        justify-content: space-evenly !important
    }
}

@media (min-width:992px) {
    .d-lg-inline {
        display: inline !important
    }

    .d-lg-inline-block {
        display: inline-block !important
    }

    .d-lg-block {
        display: block !important
    }

    .d-lg-grid {
        display: grid !important
    }

    .d-lg-table {
        display: table !important
    }

    .d-lg-table-row {
        display: table-row !important
    }

    .d-lg-table-cell {
        display: table-cell !important
    }

    .d-lg-flex {
        display: flex !important
    }

    .d-lg-inline-flex {
        display: inline-flex !important
    }

    .d-lg-none {
        display: none !important
    }

    .flex-lg-row {
        flex-direction: row !important
    }

    .flex-lg-column {
        flex-direction: column !important
    }

    .flex-lg-row-reverse {
        flex-direction: row-reverse !important
    }

    .flex-lg-column-reverse {
        flex-direction: column-reverse !important
    }

    .flex-lg-grow-0 {
        flex-grow: 0 !important
    }

    .flex-lg-grow-1 {
        flex-grow: 1 !important
    }

    .justify-content-lg-start {
        justify-content: flex-start !important
    }

    .justify-content-lg-end {
        justify-content: flex-end !important
    }

    .justify-content-lg-center {
        justify-content: center !important
    }

    .justify-content-lg-between {
        justify-content: space-between !important
    }

    .justify-content-lg-around {
        justify-content: space-around !important
    }

    .justify-content-lg-evenly {
        justify-content: space-evenly !important
    }
}

@media (min-width:1260px) {
    .d-xl-inline {
        display: inline !important
    }

    .d-xl-inline-block {
        display: inline-block !important
    }

    .d-xl-block {
        display: block !important
    }

    .d-xl-grid {
        display: grid !important
    }

    .d-xl-table {
        display: table !important
    }

    .d-xl-table-row {
        display: table-row !important
    }

    .d-xl-table-cell {
        display: table-cell !important
    }

    .d-xl-flex {
        display: flex !important
    }

    .d-xl-inline-flex {
        display: inline-flex !important
    }

    .d-xl-none {
        display: none !important
    }

    .flex-xl-row {
        flex-direction: row !important
    }

    .flex-xl-column {
        flex-direction: column !important
    }

    .flex-xl-row-reverse {
        flex-direction: row-reverse !important
    }

    .flex-xl-column-reverse {
        flex-direction: column-reverse !important
    }

    .flex-xl-grow-0 {
        flex-grow: 0 !important
    }

    .flex-xl-grow-1 {
        flex-grow: 1 !important
    }

    .justify-content-xl-start {
        justify-content: flex-start !important
    }

    .justify-content-xl-end {
        justify-content: flex-end !important
    }

    .justify-content-xl-center {
        justify-content: center !important
    }

    .justify-content-xl-between {
        justify-content: space-between !important
    }

    .justify-content-xl-around {
        justify-content: space-around !important
    }

    .justify-content-xl-evenly {
        justify-content: space-evenly !important
    }
}

@media print {
    .d-print-inline {
        display: inline !important
    }

    .d-print-inline-block {
        display: inline-block !important
    }

    .d-print-block {
        display: block !important
    }

    .d-print-grid {
        display: grid !important
    }

    .d-print-table {
        display: table !important
    }

    .d-print-table-row {
        display: table-row !important
    }

    .d-print-table-cell {
        display: table-cell !important
    }

    .d-print-flex {
        display: flex !important
    }

    .d-print-inline-flex {
        display: inline-flex !important
    }

    .d-print-none {
        display: none !important
    }
}

@font-face {
    font-family: swiper-icons;
    font-style: normal;
    font-weight: 400;
    src: url("data:application/font-woff;charset=utf-8;base64, 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") format("woff")
}

:root {
    --swiper-theme-color: #000
}

.swiper-container {
    list-style: none;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
    padding: 0;
    position: relative;
    z-index: 1
}

.swiper-container-vertical>.swiper-wrapper {
    flex-direction: column
}

.swiper-wrapper {
    box-sizing: content-box;
    display: flex;
    height: 100%;
    position: relative;
    transition-property: transform;
    width: 100%;
    z-index: 1
}

.swiper-container-android .swiper-slide,
.swiper-wrapper {
    transform: translateZ(0)
}

.swiper-container-multirow>.swiper-wrapper {
    flex-wrap: wrap
}

.swiper-container-multirow-column>.swiper-wrapper {
    flex-direction: column;
    flex-wrap: wrap
}

.swiper-container-free-mode>.swiper-wrapper {
    margin: 0 auto;
    transition-timing-function: ease-out
}

.swiper-container-pointer-events {
    touch-action: pan-y
}

.swiper-container-pointer-events.swiper-container-vertical {
    touch-action: pan-x
}

.swiper-slide {
    flex-shrink: 0;
    height: 100%;
    position: relative;
    transition-property: transform;
    width: 100%
}

.swiper-slide-invisible-blank {
    visibility: hidden
}

.swiper-container-autoheight,
.swiper-container-autoheight .swiper-slide {
    height: auto
}

.swiper-container-autoheight .swiper-wrapper {
    align-items: flex-start;
    transition-property: transform, height
}

.swiper-container-3d {
    perspective: 1200px
}

.swiper-container-3d .swiper-cube-shadow,
.swiper-container-3d .swiper-slide,
.swiper-container-3d .swiper-slide-shadow-bottom,
.swiper-container-3d .swiper-slide-shadow-left,
.swiper-container-3d .swiper-slide-shadow-right,
.swiper-container-3d .swiper-slide-shadow-top,
.swiper-container-3d .swiper-wrapper {
    transform-style: preserve-3d
}

.swiper-container-3d .swiper-slide-shadow-bottom,
.swiper-container-3d .swiper-slide-shadow-left,
.swiper-container-3d .swiper-slide-shadow-right,
.swiper-container-3d .swiper-slide-shadow-top {
    height: 100%;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 10
}

.swiper-container-3d .swiper-slide-shadow-left {
    background-image: linear-gradient(270deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-container-3d .swiper-slide-shadow-right {
    background-image: linear-gradient(90deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-container-3d .swiper-slide-shadow-top {
    background-image: linear-gradient(0deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-container-3d .swiper-slide-shadow-bottom {
    background-image: linear-gradient(180deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-container-css-mode>.swiper-wrapper {
    -ms-overflow-style: none;
    overflow: auto;
    scrollbar-width: none
}

.swiper-container-css-mode>.swiper-wrapper::-webkit-scrollbar {
    display: none
}

.swiper-container-css-mode>.swiper-wrapper>.swiper-slide {
    scroll-snap-align: start start
}

.swiper-container-horizontal.swiper-container-css-mode>.swiper-wrapper {
    scroll-snap-type: x mandatory
}

.swiper-container-vertical.swiper-container-css-mode>.swiper-wrapper {
    scroll-snap-type: y mandatory
}

:root {
    --swiper-navigation-size: 44px
}

.swiper-button-next,
.swiper-button-prev {
    align-items: center;
    color: var(--swiper-navigation-color, var(--swiper-theme-color));
    cursor: pointer;
    display: flex;
    height: var(--swiper-navigation-size);
    justify-content: center;
    margin-top: calc(0px - var(--swiper-navigation-size)/2);
    position: absolute;
    top: 50%;
    width: calc(var(--swiper-navigation-size)/44*27);
    z-index: 10
}

.swiper-button-next.swiper-button-disabled,
.swiper-button-prev.swiper-button-disabled {
    cursor: auto;
    opacity: .35;
    pointer-events: none
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-family: swiper-icons;
    font-size: var(--swiper-navigation-size);
    font-variant: normal;
    letter-spacing: 0;
    line-height: 1;
    text-transform: none !important;
    text-transform: none
}

.swiper-button-prev,
.swiper-container-rtl .swiper-button-next {
    left: 10px;
    right: auto
}

.swiper-button-prev:after,
.swiper-container-rtl .swiper-button-next:after {
    content: "prev"
}

.swiper-button-next,
.swiper-container-rtl .swiper-button-prev {
    left: auto;
    right: 10px
}

.swiper-button-next:after,
.swiper-container-rtl .swiper-button-prev:after {
    content: "next"
}

.swiper-button-next.swiper-button-white,
.swiper-button-prev.swiper-button-white {
    --swiper-navigation-color: #fff
}

.swiper-button-next.swiper-button-black,
.swiper-button-prev.swiper-button-black {
    --swiper-navigation-color: #000
}

.swiper-button-lock {
    display: none
}

.swiper-pagination {
    position: absolute;
    text-align: center;
    transform: translateZ(0);
    transition: opacity .3s;
    z-index: 10
}

.swiper-pagination.swiper-pagination-hidden {
    opacity: 0
}

.swiper-container-horizontal>.swiper-pagination-bullets,
.swiper-pagination-custom,
.swiper-pagination-fraction {
    bottom: 10px;
    left: 0;
    width: 100%
}

.swiper-pagination-bullets-dynamic {
    font-size: 0;
    overflow: hidden
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    position: relative;
    transform: scale(.33)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active,
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
    transform: scale(1)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
    transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
    transform: scale(.33)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
    transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
    transform: scale(.33)
}

.swiper-pagination-bullet {
    background: #000;
    border-radius: 50%;
    display: inline-block;
    height: 8px;
    opacity: .2;
    width: 8px
}

button.swiper-pagination-bullet {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    box-shadow: none;
    margin: 0;
    padding: 0
}

.swiper-pagination-clickable .swiper-pagination-bullet {
    cursor: pointer
}

.swiper-pagination-bullet-active {
    background: var(--swiper-pagination-color, var(--swiper-theme-color));
    opacity: 1
}

.swiper-container-vertical>.swiper-pagination-bullets {
    right: 10px;
    top: 50%;
    transform: translate3d(0, -50%, 0)
}

.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet {
    display: block;
    margin: 6px 0
}

.swiper-container-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    top: 50%;
    transform: translateY(-50%);
    width: 8px
}

.swiper-container-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    display: inline-block;
    transition: transform .2s, top .2s
}

.swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 4px
}

.swiper-container-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap
}

.swiper-container-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transition: transform .2s, left .2s
}

.swiper-container-horizontal.swiper-container-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transition: transform .2s, right .2s
}

.swiper-pagination-progressbar {
    background: rgba(0, 0, 0, .25);
    position: absolute
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    background: var(--swiper-pagination-color, var(--swiper-theme-color));
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    transform: scale(0);
    transform-origin: left top;
    width: 100%
}

.swiper-container-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    transform-origin: right top
}

.swiper-container-horizontal>.swiper-pagination-progressbar,
.swiper-container-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite {
    height: 4px;
    left: 0;
    top: 0;
    width: 100%
}

.swiper-container-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-container-vertical>.swiper-pagination-progressbar {
    height: 100%;
    left: 0;
    top: 0;
    width: 4px
}

.swiper-pagination-white {
    --swiper-pagination-color: #fff
}

.swiper-pagination-black {
    --swiper-pagination-color: #000
}

.swiper-pagination-lock {
    display: none
}

.swiper {
    position: relative
}

.swiper .swiper-container {
    position: static
}

.swiper-container-loading {
    min-height: 6.25rem
}

.swiper-container-loading:after {
    -webkit-animation: spin .4s linear infinite;
    animation: spin .4s linear infinite;
    border: 5px solid #dee2e6;
    border-radius: 50%;
    border-top-color: #adb5bd;
    content: "";
    height: 50px;
    left: 50%;
    margin-left: -25px;
    margin-top: -25px;
    position: absolute;
    top: 50%;
    width: 50px
}

.swiper-container-loading>* {
    transition: all .2s ease-in-out;
    visibility: hidden
}

@media (prefers-reduced-motion:reduce) {
    .swiper-container-loading>* {
        transition: none
    }
}

.swiper-container-loading.swiper-container-initialized {
    min-height: auto
}

.swiper-container-loading.swiper-container-initialized:after {
    display: none
}

.swiper-container-loading.swiper-container-initialized>* {
    visibility: visible
}

@-webkit-keyframes spin {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
}

.swiper-pagination-bullets {
    font-size: 0
}

.swiper-pagination-bullet {
    background-color: #000;
    height: .375rem;
    width: .375rem
}

.swiper-pagination-bullet-active {
    background-color: #20446c
}

.swiper-pagination-outside .swiper-container-horizontal .swiper-pagination-bullets {
    font-size: 0;
    margin-top: .8rem;
    position: static
}

.swiper-pagination-outside .swiper-container-horizontal .swiper-pagination-bullets-dynamic {
    margin-left: auto;
    margin-right: auto;
    transform: none
}

.swiper-button-next,
.swiper-button-prev {
    font-size: 2.5rem;
    height: auto;
    margin: 0 .5rem;
    transform: translateY(-50%);
    width: auto
}

.swiper-button-next:after,
.swiper-button-prev:after {
    display: none
}

.swiper-button-next .feather,
.swiper-button-next:after,
.swiper-button-prev .feather,
.swiper-button-prev:after {
    font-size: 1em
}

.swiper-button-next:not(.swiper-button-disabled):not(.btn),
.swiper-button-prev:not(.swiper-button-disabled):not(.btn) {
    opacity: .7;
    transition: opacity, .2s ease-in-out
}

@media (prefers-reduced-motion:reduce) {

    .swiper-button-next:not(.swiper-button-disabled):not(.btn),
    .swiper-button-prev:not(.swiper-button-disabled):not(.btn) {
        transition: none
    }
}

.swiper-button-next:not(.swiper-button-disabled):not(.btn):hover,
.swiper-button-prev:not(.swiper-button-disabled):not(.btn):hover {
    opacity: 1
}

.swiper-button-next.btn,
.swiper-button-prev.btn {
    padding: 0
}

.swiper-button-next.btn .feather,
.swiper-button-next.btn:after,
.swiper-button-prev.btn .feather,
.swiper-button-prev.btn:after {
    font-size: .5em
}

.swiper-button-prev {
    left: 0
}

.swiper-button-next {
    right: 0
}

.tingle-modal * {
    box-sizing: border-box
}

.tingle-modal {
    -webkit-overflow-scrolling: touch;
    align-items: center;
    background: rgba(0, 0, 0, .9);
    bottom: 0;
    cursor: url("data:image/svg+xml;charset=utf-8,%3Csvg width='19' height='19' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m15.514.535-6.42 6.42L2.677.536a1.517 1.517 0 0 0-2.14 0 1.517 1.517 0 0 0 0 2.14l6.42 6.419-6.42 6.419a1.517 1.517 0 0 0 0 2.14 1.517 1.517 0 0 0 2.14 0l6.419-6.42 6.419 6.42a1.517 1.517 0 0 0 2.14 0 1.517 1.517 0 0 0 0-2.14l-6.42-6.42 6.42-6.418a1.517 1.517 0 0 0 0-2.14 1.516 1.516 0 0 0-2.14 0z' fill='%23FFF'/%3E%3C/svg%3E"), auto;
    display: flex;
    flex-direction: column;
    left: 0;
    opacity: 0;
    overflow: hidden;
    position: fixed;
    right: 0;
    top: 0;
    visibility: hidden;
    z-index: 1000
}

@supports ((-webkit-backdrop-filter:blur(12px)) or (backdrop-filter:blur(12px))) {
    .tingle-modal {
        -webkit-backdrop-filter: blur(12px);
        backdrop-filter: blur(12px)
    }
}

.tingle-modal--confirm .tingle-modal-box {
    text-align: center
}

.tingle-modal--noOverlayClose {
    cursor: default
}

.tingle-modal--noClose .tingle-modal__close {
    display: none
}

.tingle-modal__close {
    background-color: transparent;
    border: none;
    color: #fff;
    cursor: pointer;
    height: 2rem;
    padding: 0;
    position: fixed;
    right: 2.5rem;
    top: 2.5rem;
    width: 2rem;
    z-index: 1000
}

.tingle-modal__close svg * {
    fill: currentColor
}

.tingle-modal__closeLabel {
    display: none
}

.tingle-modal__close:hover {
    color: #fff
}

.tingle-modal-box {
    background: #fff;
    border-radius: 4px;
    cursor: auto;
    flex-shrink: 0;
    margin-bottom: auto;
    margin-top: auto;
    opacity: 1;
    position: relative;
    width: 60%;
    will-change: transform, opacity
}

.tingle-modal-box__content {
    padding: 3rem
}

.tingle-modal-box__footer {
    background-color: #f5f5f5;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    cursor: auto;
    padding: 1.5rem 2rem;
    width: auto
}

.tingle-modal-box__footer:after {
    clear: both;
    content: "";
    display: table
}

.tingle-modal-box__footer--sticky {
    bottom: -200px;
    opacity: 1;
    position: fixed;
    transition: bottom .3s ease-in-out .3s;
    z-index: 10001
}

.tingle-enabled {
    left: 0;
    overflow: hidden;
    position: fixed;
    right: 0
}

.tingle-modal--visible .tingle-modal-box__footer {
    bottom: 0
}

.tingle-modal--visible {
    opacity: 1;
    visibility: visible
}

.tingle-modal--visible .tingle-modal-box {
    -webkit-animation: scale .2s cubic-bezier(.68, -.55, .265, 1.55) forwards;
    animation: scale .2s cubic-bezier(.68, -.55, .265, 1.55) forwards
}

.tingle-modal--overflow {
    overflow-y: scroll;
    padding-top: 8vh
}

.tingle-btn {
    background-color: grey;
    border: none;
    box-shadow: none;
    color: #fff;
    cursor: pointer;
    display: inline-block;
    font-family: inherit;
    font-size: inherit;
    line-height: normal;
    margin: 0 .5rem;
    padding: 1rem 2rem;
    text-decoration: none;
    transition: background-color .4s ease;
    vertical-align: middle
}

.tingle-btn--primary {
    background-color: #3498db
}

.tingle-btn--danger {
    background-color: #e74c3c
}

.tingle-btn--default {
    background-color: #34495e
}

.tingle-btn--pull-left {
    float: left
}

.tingle-btn--pull-right {
    float: right
}

@media (max-width:540px) {
    .tingle-modal {
        display: block;
        padding-top: 60px;
        top: 0;
        width: 100%
    }

    .tingle-modal-box {
        border-radius: 0;
        width: auto
    }

    .tingle-modal-box__content {
        overflow-y: scroll
    }

    .tingle-modal--noClose {
        top: 0
    }

    .tingle-modal--noOverlayClose {
        padding-top: 0
    }

    .tingle-modal-box__footer .tingle-btn {
        display: block;
        float: none;
        margin-bottom: 1rem;
        width: 100%
    }

    .tingle-modal__close {
        background-color: #2c3e50;
        border: none;
        box-shadow: none;
        color: #fff;
        display: block;
        height: 60px;
        left: 0;
        right: 0;
        top: 0;
        width: 100%
    }

    .tingle-modal__closeLabel {
        display: inline-block;
        font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
        font-size: 1.6rem;
        vertical-align: middle
    }

    .tingle-modal__closeIcon {
        display: inline-block;
        font-size: 0;
        margin-right: .8rem;
        vertical-align: middle;
        width: 1.6rem
    }
}

@-webkit-keyframes scale {
    0% {
        opacity: 0;
        transform: scale(.9)
    }

    to {
        opacity: 1;
        transform: scale(1)
    }
}

@keyframes scale {
    0% {
        opacity: 0;
        transform: scale(.9)
    }

    to {
        opacity: 1;
        transform: scale(1)
    }
}

.tingle-modal {
    background-color: rgba(0, 0, 0, .75);
    cursor: default;
    -webkit-user-select: auto;
    -moz-user-select: auto;
    user-select: auto;
    z-index: 1060
}

@media screen and (max-width:539px) {
    .tingle-modal {
        padding-top: 0
    }
}

@media (min-width:992px) {
    .tingle-modal--overflow {
        padding-bottom: 5vh;
        padding-top: 5vh
    }
}

@media screen and (max-width:539px) {
    .tingle-modal .tingle-modal__close {
        align-items: center;
        background-color: transparent;
        color: #000;
        display: flex;
        height: 40px;
        justify-content: center;
        left: auto;
        padding: 10px 24px;
        text-align: right;
        width: auto
    }

    .tingle-modal .tingle-modal__close .tingle-modal__closeIcon {
        margin-right: 0;
        width: 15px
    }

    .tingle-modal .tingle-modal__close .tingle-modal__closeLabel {
        display: none
    }
}

.tingle-modal .modal-title {
    color: #20446c;
    font-weight: 700;
    line-height: 1.8;
    margin-bottom: 1rem;
    text-transform: uppercase
}

.tingle-modal .text-highlight {
    color: #dc3545
}

.tingle-modal h1,
.tingle-modal h2,
.tingle-modal h3,
.tingle-modal h4,
.tingle-modal h5,
.tingle-modal h6 {
    margin-bottom: 16px
}

.tingle-modal h1 {
    font-size: 35px
}

.tingle-modal h2 {
    font-size: 32px
}

.tingle-modal h3 {
    font-size: 28px
}

.tingle-modal h4 {
    font-size: 24px
}

.tingle-modal h5 {
    font-size: 21px
}

.tingle-modal h6 {
    font-size: 16px
}

.tingle-modal p {
    margin-bottom: 16px
}

@media screen and (max-width:539px) {
    .tingle-modal--iframe {
        display: flex
    }

    .tingle-modal--iframe .tingle-modal__close {
        color: #fff
    }
}

.tingle-modal--iframe .tingle-modal-box {
    background-color: #000;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, .175);
    width: 60%
}

@media (max-width:575.98px) {
    .tingle-modal--iframe .tingle-modal-box {
        width: 100%
    }
}

.tingle-modal--iframe .tingle-modal-box__content {
    padding: 56.25% 0 0
}

.tingle-modal--iframe .tingle-modal-box iframe {
    border: 0;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%
}

.tingle-modal--close-inside .tingle-modal__close {
    background-color: #000
}

@media (min-width:992px) {
    .tingle-modal--close-inside .tingle-modal__close {
        display: none
    }
}

.tingle-modal--close-inside .tingle-modal__closeLabel {
    font-size: 14px
}

.tingle-modal--close-inside .tingle-modal-box__footer {
    padding: 0
}

.tingle-modal--close-inside .tingle-close-inside {
    background-color: hsla(0, 0%, 100%, 0);
    border: none;
    border-radius: 50%;
    height: 6.25rem;
    outline: none;
    padding: 0;
    position: absolute;
    right: 0;
    text-indent: -9999px;
    top: 2.5rem;
    width: 6.25rem
}

.tingle-modal--form {
    font-size: 20px
}

@media screen and (min-width:540px) {
    .tingle-modal--form .tingle-modal-box {
        width: 500px
    }
}

.tingle-modal--form .tingle-modal-box__content {
    padding: 48px
}

@media (max-width:575.98px) {
    .tingle-modal--form .tingle-modal-box__content {
        padding: 48px 24px
    }
}

.tingle-modal--form .modal-title {
    line-height: 1.2;
    margin-bottom: 1.5rem
}

.tingle-modal--form .form-login-info {
    margin-bottom: 1.5rem
}

.tingle-modal--form .form-group {
    margin-bottom: 16px
}

.tingle-modal--form .form-group:last-child {
    margin-bottom: 0
}

.tingle-modal--form .btn,
.tingle-modal--form .form-control,
.tingle-modal--form .form-select {
    border-radius: 4px;
    font-size: 1em;
    height: auto
}

.tingle-modal--form .btn,
.tingle-modal--form .form-control {
    padding: 8px 16px
}

.tingle-modal--form .form-select {
    padding: 8px 28px 8px 16px
}

.tingle-modal--style-1 .tingle-modal-box {
    font-size: 16px
}

@media screen and (min-width:541px) {
    .tingle-modal--style-1 .tingle-modal-box {
        width: 80%
    }
}

@media (min-width:992px) {
    .tingle-modal--style-1 .tingle-modal-box {
        background: url(../images/popup.png) no-repeat top;
        background-size: 100% 100%;
        display: flex;
        font-size: 1.125rem;
        height: 33.8125rem;
        width: 69.4375rem
    }
}

.tingle-modal--style-1 .tingle-modal-box__content {
    padding: 48px 24px
}

@media screen and (min-width:541px) {
    .tingle-modal--style-1 .tingle-modal-box__content {
        padding: 48px
    }
}

@media (min-width:992px) {
    .tingle-modal--style-1 .tingle-modal-box__content {
        display: flex;
        flex-grow: 1;
        padding: 5rem 9.375rem 6.25rem
    }

    .tingle-modal--style-1 .tingle-modal-box__content p:last-of-type {
        margin-bottom: 0
    }
}

.tingle-modal--style-1 .perfect-scrollbar {
    flex-grow: 1;
    max-height: 31.25rem
}

@media (max-width:991.98px) {
    .tingle-modal--style-1 .perfect-scrollbar {
        max-height: none
    }
}

.tingle-modal--style-1 .perfect-scrollbar.ps--active-y {
    margin-right: -.75rem;
    padding-right: .75rem
}

.ps {
    -ms-overflow-style: none;
    overflow: hidden !important;
    overflow-anchor: none;
    touch-action: auto;
    -ms-touch-action: auto
}

.ps__rail-x {
    bottom: 0;
    height: 15px
}

.ps__rail-x,
.ps__rail-y {
    display: none;
    opacity: 0;
    position: absolute;
    transition: background-color .2s linear, opacity .2s linear;
    -webkit-transition: background-color .2s linear, opacity .2s linear
}

.ps__rail-y {
    right: 0;
    width: 15px
}

.ps--active-x>.ps__rail-x,
.ps--active-y>.ps__rail-y {
    background-color: transparent;
    display: block
}

.ps--focus>.ps__rail-x,
.ps--focus>.ps__rail-y,
.ps--scrolling-x>.ps__rail-x,
.ps--scrolling-y>.ps__rail-y,
.ps:hover>.ps__rail-x,
.ps:hover>.ps__rail-y {
    opacity: .6
}

.ps .ps__rail-x.ps--clicking,
.ps .ps__rail-x:focus,
.ps .ps__rail-x:hover,
.ps .ps__rail-y.ps--clicking,
.ps .ps__rail-y:focus,
.ps .ps__rail-y:hover {
    background-color: #eee;
    opacity: .9
}

.ps__thumb-x {
    bottom: 2px;
    height: 6px;
    transition: background-color .2s linear, height .2s ease-in-out;
    -webkit-transition: background-color .2s linear, height .2s ease-in-out
}

.ps__thumb-x,
.ps__thumb-y {
    background-color: #aaa;
    border-radius: 6px;
    position: absolute
}

.ps__thumb-y {
    right: 2px;
    transition: background-color .2s linear, width .2s ease-in-out;
    -webkit-transition: background-color .2s linear, width .2s ease-in-out;
    width: 6px
}

.ps__rail-x.ps--clicking .ps__thumb-x,
.ps__rail-x:focus>.ps__thumb-x,
.ps__rail-x:hover>.ps__thumb-x {
    background-color: #999;
    height: 11px
}

.ps__rail-y.ps--clicking .ps__thumb-y,
.ps__rail-y:focus>.ps__thumb-y,
.ps__rail-y:hover>.ps__thumb-y {
    background-color: #999;
    width: 11px
}

@supports (-ms-overflow-style:none) {
    .ps {
        overflow: auto !important
    }
}

@media (-ms-high-contrast:none),
screen and (-ms-high-contrast:active) {
    .ps {
        overflow: auto !important
    }
}

.perfect-scrollbar {
    -webkit-overflow-scrolling: touch;
    max-height: 300px;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative
}

.ps .ps__rail-y {
    z-index: 1
}

.ps .ps__rail-y.ps--clicking,
.ps .ps__rail-y:hover {
    background-color: transparent
}

.ps .ps__rail-y .ps__thumb-y,
.ps .ps__thumb-y {
    background-color: #20446c
}

.ps .ps__thumb-y {
    border-radius: 0;
    right: 0
}

@font-face {
    font-family: Roboto;
    font-style: normal;
    font-weight: 400;
    src: url(../fonts/roboto/Roboto-Regular.woff2) format("woff2")
}

@font-face {
    font-family: Roboto;
    font-style: normal;
    font-weight: 500;
    src: url(../fonts/roboto/Roboto-Medium.woff2) format("woff2")
}

@font-face {
    font-family: Roboto Condensed;
    font-style: normal;
    font-weight: 400;
    src: url(../fonts/roboto/RobotoCondensed-Regular.woff2) format("woff2")
}

@font-face {
    font-family: VL Abraham Lincoln;
    font-style: normal;
    font-weight: 400;
    src: url(../fonts/vl-abraham-lincoln/VLAbrahamLincoln.woff2) format("woff2")
}

.sr-only {
    clip: rect(0, 0, 0, 0) !important;
    border: 0 !important;
    height: 1px !important;
    margin: -1px !important;
    overflow: hidden !important;
    padding: 0 !important;
    position: absolute !important;
    white-space: nowrap !important;
    width: 1px !important
}

.img-fluid {
    height: auto;
    max-width: 100%
}

.img-block {
    display: block;
    height: auto;
    width: 100%
}

.text-center {
    text-align: center !important
}

@media (min-width:992px) {

    html:not(.mac)::-webkit-scrollbar,
    html:not(.mac) ::-webkit-scrollbar {
        background-color: transparent;
        width: 10px
    }

    html:not(.mac)::-webkit-scrollbar-thumb,
    html:not(.mac) ::-webkit-scrollbar-thumb {
        background-color: #b3b3b3;
        -webkit-transition: all .2s ease-in-out;
        transition: all .2s ease-in-out
    }
}

@media (min-width:992px) and (prefers-reduced-motion:reduce) {

    html:not(.mac)::-webkit-scrollbar-thumb,
    html:not(.mac) ::-webkit-scrollbar-thumb {
        -webkit-transition: none;
        transition: none
    }
}

@media (min-width:992px) {

    html:not(.mac)::-webkit-scrollbar-thumb:focus,
    html:not(.mac) ::-webkit-scrollbar-thumb:focus,
    html:not(.mac)::-webkit-scrollbar-thumb:hover,
    html:not(.mac) ::-webkit-scrollbar-thumb:hover {
        background-color: #999
    }

    html:not(.mac)::-webkit-scrollbar-track,
    html:not(.mac) ::-webkit-scrollbar-track {
        background-color: #fff
    }
}

@media (max-width:991.98px) {
    html {
        font-size: 1.66667vw
    }
}

.page,
body {
    overflow: hidden
}

.page {
    background: #020101 url(../images/bg.jpg) no-repeat top;
    display: flex;
    flex-direction: column;
    margin-left: auto;
    margin-right: auto;
    max-width: 1920px;
    min-height: 178.875rem;
    position: relative
}

@media (max-width:991.98px) {
    .page {
        background-image: url(../images/bg-m.jpg);
        background-size: 100%;
        margin-top: 6.3125rem
    }
}

@media screen and (min-width:1920px) {
    .page {
        font-size: 1rem
    }
}

@media (max-width:991.98px) {
    .container {
        max-width: none
    }
}

.header {
    align-items: center;
    display: none;
    position: relative;
    z-index: 1040
}

@media (max-width:991.98px) {
    .header {
        background-color: rgba(0, 0, 0, .9);
        display: flex;
        height: 6.375rem;
        left: 0;
        position: fixed;
        right: 0;
        top: 0
    }
}

.header .container {
    align-items: center;
    display: flex;
    justify-content: space-between;
    position: relative
}

.header-logo {
    display: inline-block;
    width: 5.8125rem
}

.header-buttons {
    display: flex
}

.header-buttons .btn-img:not(:last-child) {
    margin-right: .5rem
}

.footer {
    color: #fff;
    font-size: 1rem;
    margin-top: auto;
    padding: 2.1875rem 0
}

@media (max-width:991.98px) {
    .footer {
        font-size: 1.5rem
    }
}

.footer a {
    color: #fff
}

.footer a:focus,
.footer a:hover {
    color: #0060ae
}

.footer-logo {
    display: inline-block;
    margin-bottom: 1em;
    margin-top: -.875em;
    width: 10.625em
}

.footer-logo img {
    display: block;
    height: auto;
    width: 100%
}

.footer .fs-lg {
    font-size: 130%
}

[class*=btn-hover] {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.btn-hover-heartbeat:focus,
.btn-hover-heartbeat:hover {
    -webkit-animation-name: heartBeat;
    animation-name: heartBeat
}

.btn-hover-headshake:focus,
.btn-hover-headshake:hover {
    -webkit-animation-name: headShake;
    animation-name: headShake
}

.btn-img {
    -webkit-animation-name: pulse-reverse;
    animation-name: pulse-reverse;
    background-color: transparent;
    border: none;
    border-radius: 0;
    display: inline-block;
    padding: 0;
    transition: filter .3s ease-in-out;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.btn-img:focus {
    outline: none
}

@media (prefers-reduced-motion:reduce) {
    .btn-img {
        transition: none
    }
}

a.btn-img:focus,
a.btn-img:hover,
button.btn-img:focus,
button.btn-img:hover {
    filter: brightness(1.5)
}

.btn-img img {
    display: block;
    height: auto;
    width: 100%
}

.btn-choi-ngay {
    height: auto;
    width: 9.3125rem
}

.btn-appstore,
.btn-fanpage,
.btn-googleplay,
.btn-group-2,
.btn-mua-scoin,
.btn-nap-the-2 {
    height: auto;
    width: 9.1875rem
}

.btn-nhan-qua {
    height: auto;
    width: 7.125rem
}

.btn-facebook,
.btn-group {
    height: auto;
    width: 5.6875rem
}

.btn-vao-game {
    height: auto;
    width: 12.75rem
}

.btn-nap-the {
    height: auto;
    width: 12.125rem
}

.text-effect-light-slide-x {
    background-image: url(../images/home/<USER>/title.png);
    background-repeat: no-repeat;
    background-size: 100%;
    -webkit-mask-image: url(../images/home/<USER>/title.png);
    mask-image: url(../images/home/<USER>/title.png);
    -webkit-mask-size: 100%;
    mask-size: 100%;
    position: relative
}

.text-effect-light-slide-x:after {
    -webkit-animation: light-slide-x 5s linear infinite;
    animation: light-slide-x 5s linear infinite;
    background: url(../images/light.png) 300% 0 no-repeat;
    background-size: contain;
    bottom: 0;
    content: "";
    height: 100%;
    left: 0;
    opacity: .7;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%
}

.text-effect-light-slide-x>img {
    visibility: hidden
}

@-webkit-keyframes light-slide-x {
    0% {
        background-position: 200% 0
    }

    to {
        background-position: -100% 0
    }
}

@keyframes light-slide-x {
    0% {
        background-position: 200% 0
    }

    to {
        background-position: -100% 0
    }
}

.download {
    align-items: center;
    background: url(../images/download/download-frame.png) no-repeat 0 0;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    height: 45.625rem;
    padding: 2.0625rem 2.625rem 0 5.125rem;
    position: fixed;
    right: 0;
    top: 50%;
    transform: translate(100%, -50%);
    transition: transform .4s ease-in-out;
    width: 18.1875rem
}

@media (prefers-reduced-motion:reduce) {
    .download {
        transition: none
    }
}

@media (max-width:991.98px) {
    .download {
        display: none
    }
}

.download-btn:not(:last-child) {
    margin-bottom: .4375rem
}

.download-btn:nth-child(5) {
    margin-bottom: .74375rem
}

.download-btn:nth-child(6) {
    margin-bottom: .125rem
}

.download-divider {
    background-image: linear-gradient(90deg, hsla(0, 0%, 100%, 0), #ff7800, hsla(0, 0%, 100%, 0));
    border: none;
    height: 1px;
    margin: 0 0 .4375rem;
    width: 9.125rem
}

.download-backtotop {
    bottom: 7.875rem;
    height: 3.25rem;
    left: 5.125rem;
    position: absolute;
    width: 10.4375rem
}

.download-toggle {
    background: url(../images/download-sticky-arrow.png) no-repeat 100%;
    background-size: 100% 100%;
    height: 2.9375rem;
    position: absolute;
    right: 100%;
    top: 50%;
    transition: all .2s ease-in-out;
    width: 1.5625rem
}

@media (prefers-reduced-motion:reduce) {
    .download-toggle {
        transition: none
    }
}

.download.active {
    transform: translateY(-50%)
}

.loader {
    align-items: center;
    background: #090707 url(../images/bg-loading.jpg) no-repeat top;
    background-size: cover;
    bottom: 0;
    color: #000;
    display: flex;
    font-size: .75rem;
    justify-content: center;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 9999
}

.loader-text {
    display: inline-block;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%)
}

.loader-progress {
    height: 2px;
    width: 20%
}

@media (max-width:991.98px) {
    .loader-progress {
        width: 50%
    }
}

:root {
    --sk-color: #090707;
    --sk-size: 5rem
}

.sk-circle-fade {
    height: var(--sk-size);
    position: relative;
    width: var(--sk-size)
}

.sk-circle-fade-dot {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.sk-circle-fade-dot:before {
    -webkit-animation: sk-circle-fade 1.2s ease-in-out infinite both;
    animation: sk-circle-fade 1.2s ease-in-out infinite both;
    background-color: var(--sk-color);
    border-radius: 100%;
    content: "";
    display: block;
    height: 15%;
    width: 15%
}

.sk-circle-fade-dot:first-child {
    transform: rotate(30deg)
}

.sk-circle-fade-dot:nth-child(2) {
    transform: rotate(60deg)
}

.sk-circle-fade-dot:nth-child(3) {
    transform: rotate(90deg)
}

.sk-circle-fade-dot:nth-child(4) {
    transform: rotate(120deg)
}

.sk-circle-fade-dot:nth-child(5) {
    transform: rotate(150deg)
}

.sk-circle-fade-dot:nth-child(6) {
    transform: rotate(180deg)
}

.sk-circle-fade-dot:nth-child(7) {
    transform: rotate(210deg)
}

.sk-circle-fade-dot:nth-child(8) {
    transform: rotate(240deg)
}

.sk-circle-fade-dot:nth-child(9) {
    transform: rotate(270deg)
}

.sk-circle-fade-dot:nth-child(10) {
    transform: rotate(300deg)
}

.sk-circle-fade-dot:nth-child(11) {
    transform: rotate(330deg)
}

.sk-circle-fade-dot:first-child:before {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s
}

.sk-circle-fade-dot:nth-child(2):before {
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}

.sk-circle-fade-dot:nth-child(3):before {
    -webkit-animation-delay: -.9s;
    animation-delay: -.9s
}

.sk-circle-fade-dot:nth-child(4):before {
    -webkit-animation-delay: -.8s;
    animation-delay: -.8s
}

.sk-circle-fade-dot:nth-child(5):before {
    -webkit-animation-delay: -.7s;
    animation-delay: -.7s
}

.sk-circle-fade-dot:nth-child(6):before {
    -webkit-animation-delay: -.6s;
    animation-delay: -.6s
}

.sk-circle-fade-dot:nth-child(7):before {
    -webkit-animation-delay: -.5s;
    animation-delay: -.5s
}

.sk-circle-fade-dot:nth-child(8):before {
    -webkit-animation-delay: -.4s;
    animation-delay: -.4s
}

.sk-circle-fade-dot:nth-child(9):before {
    -webkit-animation-delay: -.3s;
    animation-delay: -.3s
}

.sk-circle-fade-dot:nth-child(10):before {
    -webkit-animation-delay: -.2s;
    animation-delay: -.2s
}

.sk-circle-fade-dot:nth-child(11):before {
    -webkit-animation-delay: -.1s;
    animation-delay: -.1s
}

@-webkit-keyframes sk-circle-fade {

    0%,
    39%,
    to {
        opacity: 0;
        transform: scale(.6)
    }

    40% {
        opacity: 1;
        transform: scale(1)
    }
}

@keyframes sk-circle-fade {

    0%,
    39%,
    to {
        opacity: 0;
        transform: scale(.6)
    }

    40% {
        opacity: 1;
        transform: scale(1)
    }
}

.gift {
    top: 18.063rem;
    display: flex;
    flex-direction: column;
    left: 15.5rem;
    max-width: 32.563rem;
    position: absolute;
    width: 32.563rem;
    z-index: 1030
}

@media (max-width:991.98px) {
    .gift {
        width: 24.563rem;
        left: 0;
        top: 44rem;
    }
}

.countdown {
    color: #d3e5ff;
    display: flex;
    font-family: VL Abraham Lincoln, sans-serif;
    font-size: 3.625rem;
    line-height: 1.4;
    position: relative;
    z-index: 0
}

.countdown-item {
    align-items: center;
    display: flex
}

.countdown-item:not(:first-child) {
    margin-left: 5rem
}

.countdown-num {
    font-weight: 900;
    position: relative;
    text-align: center
}

.countdown-text {
    margin-left: .5rem;
    position: relative
}

.countdown-shadow .countdown-num:after {
    align-items: center;
    display: flex;
    justify-content: center;
    padding-top: .3125rem;
    text-shadow: -.125rem .125rem .3125rem rgba(0, 0, 0, .5)
}

.countdown-shadow .countdown-num:after,
.countdown-shadow .countdown-text:after {
    bottom: 0;
    content: attr(data-content);
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: -1
}

.countdown-shadow .countdown-text:after {
    text-shadow: -.125rem .125rem .125rem rgba(0, 0, 0, .5)
}

.section-bg {
    background-position: top;
    background-repeat: no-repeat;
    background-size: 100%
}

a.section-btn-play:focus,
a.section-btn-play:hover,
button.section-btn-play:focus,
button.section-btn-play:hover {
    filter: brightness(1.3)
}

.section-1 {
    height: 64.875rem;
    position: relative;
    z-index: 0
}

.section-1 .container {
    align-items: center;
    display: flex;
    flex-direction: column;
    padding-top: .875rem;
    position: relative
}

.section-1 .container>* {
    margin-bottom: 1rem
}

.section-1 .section-logo {
    display: inline-block;
    left: 50%;
    position: absolute;
    top: .875rem;
    transform: translateX(-50%);
    width: 17.375rem
}

.section-1 .section-title {
    width: 29rem;
    margin-top: 12rem;
}

.section-2 {
    align-items: center;
    display: flex;
    flex-direction: column;
    position: relative
}

.section-2 .section-title {
    margin-bottom: 48px;
    width: 42.75rem
}

.section-2 .section-reward {
    display: block;
    margin-top: -4.1875rem;
    width: 25.0625rem
}

.section-2 .section-countdown {
    margin-top: -.875rem
}

.section-2 .section-title-2 {
    margin-bottom: 1.375rem;
    width: 45rem
}

.section-2 .row {
    margin-top: -3.125rem
}

@media (max-width:991.98px) {
    .section-2 .row {
        margin-left: 0;
        margin-right: 0;
        width: 100%
    }

    .section-2 .row>.col,
    .section-2 .row>[class*=col-] {
        padding-left: 0;
        padding-right: 0
    }
}

.section-2 .row [class*=col-] {
    margin-top: 3.125rem
}

.ranking {
    background: url(../images/home/<USER>/frame.png) no-repeat 0 0;
    background-size: 100% 100%;
    color: #2c383f;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    font-size: 1.875rem;
    height: 46.4375rem;
    max-width: 66.8125rem;
    padding: 1.875rem 5.375rem;
    position: relative;
    width: 100%
}

.ranking>.table:first-child {
    margin-bottom: 1.25rem
}

.ranking>.table:first-child thead th {
    padding-bottom: 0;
    color: #fff;
}

.ranking .perfect-scrollbar {
    max-height: none
}

.table-ranking {
    color: inherit;
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 0;
    text-align: center
}

.table-ranking img {
    margin: 0 auto
}

.table-ranking td,
.table-ranking th {
    padding: .625rem;
    vertical-align: middle;
    width: 25%
}

.table-ranking td:first-child,
.table-ranking th:first-child {
    width: 20%
}

.table-ranking td:nth-child(2),
.table-ranking th:nth-child(2) {
    width: 30%
}

.table-ranking thead th {
    font-family: Roboto Condensed, sans-serif;
    font-weight: 400;
    text-transform: uppercase;
    white-space: nowrap
}

.table-ranking tbody th {
    font-weight: 500
}

.table-ranking .table-top {
    font-family: VL Abraham Lincoln, sans-serif;
    font-size: 1.98rem;
    line-height: 1
}

.table-ranking .table-top td,
.table-ranking .table-top th {
    padding-bottom: 0;
    padding-top: 0
}

.table-ranking .table-top-number {
    align-items: center;
    background: url(../images/home/<USER>/top-bg.png) no-repeat 0 0;
    background-size: 100% 100%;
    color: #fff;
    display: flex;
    font-size: 1.875rem;
    height: 6.24375rem;
    justify-content: center;
    margin: auto;
    width: 6.3rem;
}

.table-ranking-no-head thead td,
.table-ranking-no-head thead th {
    font-size: 0;
    padding: 0;
    text-indent: -9999px
}

.table-pe-0 td:first-child,
.table-pe-0 th:first-child {
    padding-left: 0
}

.table-pe-0 td:last-child,
.table-pe-0 th:last-child {
    padding-right: 0
}

.table-pe-0 thead tr:first-child td,
.table-pe-0 thead tr:first-child th {
    padding-top: 0
}

.table-pe-0 thead+tbody tr:first-child td,
.table-pe-0 thead+tbody tr:first-child th {
    padding-top: .5rem
}

.table-pe-0 tbody tr:first-child td,
.table-pe-0 tbody tr:first-child th {
    padding-top: 0
}

.table-pe-0 tbody tr:last-child td,
.table-pe-0 tbody tr:last-child th {
    padding-bottom: 0
}

.section-3 {
    height: 64.125rem;
    padding: 0;
    position: relative
}

@media (max-width:991.98px) {
    .section-3 {
        height: 67.5rem
    }
}

.section-3 .container {
    align-items: center;
    display: flex;
    flex-direction: column
}

.section-3 .section-title {
    margin-top: -10.25rem;
    width: 35.9375rem
}

.section-3 .btn-img-group {
    margin-bottom: .625rem;
    position: relative;
    z-index: 1
}

.section-3 .btn-img-group-2 {
    margin-bottom: 0;
    margin-top: -2.1875rem
}

.section-3 .section-btn-play {
    margin-bottom: .625rem;
    margin-left: -4.0625rem;
    position: relative;
    z-index: 1
}

.btn-img-group {
    display: flex
}

.btn-img-group .btn-img:not(:first-child) {
    margin-left: -1.125rem
}

.tc {
    flex-grow: 1;
    height: 36.0625rem;
    position: relative;
    width: 100%
}

.tc:after {
    background: url(../images/home/<USER>/bg.png) no-repeat 50%;
    background-size: 100% 100%;
    content: "";
    height: 55.625rem;
    left: 50%;
    margin-top: 1.9375rem;
    pointer-events: none;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 90.9375rem
}

.tc-item,
.tc:after {
    position: absolute
}

.tc-item-1 {
    left: 0;
    margin-left: -4.84694%;
    margin-top: -14.88095%;
    top: 0;
    width: 28.40136%
}

.tc-item-2 {
    margin-right: -2.72109%;
    margin-top: -12.58503%;
    right: 0;
    top: 0;
    width: 35.37415%
}

.tc-item-3 {
    bottom: 0;
    margin-bottom: .42517%;
    margin-right: -8.5034%;
    right: 0;
    width: 28.14626%
}

.tc-item-4 {
    bottom: 0;
    left: 0;
    margin-bottom: -1.53061%;
    margin-left: -1.87075%;
    width: 28.40136%
}

.tc-item-5 {
    left: 50%;
    margin-top: -3.40136%;
    position: absolute;
    top: 0;
    transform: translateX(-50%);
    width: 57.82313%
}

@media (max-width:991.98px) {
    .tc-item-5 {
        margin-top: -2.55102%
    }
}

.section-4 .container {
    align-items: center;
    display: flex;
    flex-direction: column;
    padding: 0
}

.section-4 .section-title {
    margin-bottom: 1.25rem;
    width: 34.5625rem
}

@media (max-width:991.98px) {
    .section-4 .section-title {
        margin-bottom: 2.5rem
    }
}

.section-4 .swiper {
    background: url(../images/home/<USER>/frame.png) no-repeat 50%;
    background-size: 100% 100%;
    max-width: 74.3125rem;
    padding: 5.8873% 9.08326% 6.39193%
}

@media (max-width:991.98px) {
    .section-4 .swiper {
        background-image: url(../images/home/<USER>/frame-m.jpg);
        padding: 2% 0
    }
}

.section-4 .swiper .swiper-pagination-bullets {
    bottom: -1.875rem
}

@media (max-width:991.98px) {
    .section-4 .swiper .swiper-pagination-bullets {
        bottom: auto;
        margin-top: 1rem;
        top: 100%
    }
}

.section-4 .swiper .swiper-pagination-bullet {
    background: url(../images/home/<USER>/swiper-pagination.png) no-repeat 0 0;
    background-size: 100% 100%;
    height: 4.1875rem;
    margin: 0 .1875rem;
    opacity: 1;
    outline: none;
    width: 3.75rem
}

.section-4 .swiper .swiper-pagination-bullet-active {
    background-image: url(../images/home/<USER>/swiper-pagination-active.png)
}
