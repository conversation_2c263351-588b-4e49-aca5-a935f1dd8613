var target_date = new Date(TimeEnd).getTime() /1000; // set the countdown date
var days, hours, minutes, seconds; // variables for time units

var countdown = document.getElementById("timexs"); // get tag element

getCountdown();

var x = setInterval(function () { getCountdown(); }, 1000);

function getCountdown() {

    // find the amount of "seconds" between now and target
    var current_date = new Date().getTime() / 1000;
    var seconds_left = (target_date - current_date);
    if (seconds_left > 0) {
        days = pad(parseInt(seconds_left / 86400));
        seconds_left = seconds_left % 86400;

        hours = pad(parseInt(seconds_left / 3600));
        seconds_left = seconds_left % 3600;

        minutes = pad(parseInt(seconds_left / 60));
        seconds = pad(parseInt(seconds_left % 60));

        // format countdown string + set tag value
        var htmlx = '<div class="countdown-item">CÒN</div>';
        htmlx += '<div class="countdown-item">';
        htmlx += '  <span class="countdown-num" data-content="' + days + '">' + days + '</span>';
        htmlx += '  <span class="countdown-text" data-content="Ngày">Ngày</span>';
        htmlx += '</div>';
        htmlx += '<div class="countdown-item">';
        htmlx += '  <span class="countdown-num" data-content="' + hours + '">' + hours + '</span>';
        htmlx += '  <span class="countdown-text" data-content="Giờ">Giờ</span>';
        htmlx += '</div>';
        htmlx += '<div class="countdown-item">';
        htmlx += '  <span class="countdown-num" data-content="' + minutes + '">' + minutes + '</span>';
        htmlx += '  <span class="countdown-text" data-content="Phút">Phút</span>';
        htmlx += '</div>';
        //countdown.innerHTML = "<span>" + days + "</span><span>" + hours + "</span><span>" + minutes + "</span><span>" + seconds + "</span>";
        countdown.innerHTML = htmlx;
    }
    else {
        var htmlx = '<div class="countdown-item">CÒN</div>';
        htmlx += '<div class="countdown-item">';
        htmlx += '  <span class="countdown-num" data-content="0">0</span>';
        htmlx += '  <span class="countdown-text" data-content="Ngày">Ngày</span>';
        htmlx += '</div>';
        htmlx += '<div class="countdown-item">';
        htmlx += '  <span class="countdown-num" data-content="0">0</span>';
        htmlx += '  <span class="countdown-text" data-content="Giờ">Giờ</span>';
        htmlx += '</div>';
        htmlx += '<div class="countdown-item">';
        htmlx += '  <span class="countdown-num" data-content="0">0</span>';
        htmlx += '  <span class="countdown-text" data-content="Phút">Phút</span>';
        htmlx += '</div>';
        countdown.innerHTML = htmlx;
        clearInterval(x);
    }
}

function pad(n) {
    return (n < 10 ? '0' : '') + n;
}
