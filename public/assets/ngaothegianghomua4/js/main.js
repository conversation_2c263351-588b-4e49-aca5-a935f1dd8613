'use strict';

/* exported extend, ready, getScrollbarWidth */
let extend = function(out) {
  out = out || {};

  for (let i = 1; i < arguments.length; i++) {
    if (!arguments[i]) {
      continue;
    }

    for (let key in arguments[i]) {
      if (Object.prototype.hasOwnProperty.call(arguments[i], key)) {
        out[key] = arguments[i][key];
      }
    }
  }

  return out;
};

function ready(fn) {
  if (document.readyState !== 'loading') {
    fn();
  } else {
    document.addEventListener('DOMContentLoaded', fn);
  }
}


// detect Mac OS X
if (navigator.platform.toUpperCase().indexOf('MAC') >= 0) {
  document.getElementsByTagName('html')[0].classList.add('mac');
}

// get scrollbar width
function getScrollbarWidth() {
  // Creating invisible container
  const outer = document.createElement('div');
  outer.style.visibility = 'hidden';
  outer.style.overflow = 'scroll'; // forcing scrollbar to appear
  outer.style.msOverflowStyle = 'scrollbar'; // needed for WinJS apps
  document.body.appendChild(outer);

  // Creating inner element and placing it in the container
  const inner = document.createElement('div');
  outer.appendChild(inner);

  // Calculating difference between container's full width and the child width
  const scrollbarWidth = (outer.offsetWidth - inner.offsetWidth);

  // Removing temporary elements from the DOM
  outer.parentNode.removeChild(outer);

  return scrollbarWidth;
}

document.getElementById('loader').style.paddingRight = getScrollbarWidth() + 'px';


// -------------------------
// back to top
// -------------------------

/* global ready, SmoothScroll */
ready(() => {
  'use strict';

  new SmoothScroll('#backtotop', {
    speedAsDuration: true,
    updateURL: false
  });
});


// --------------------------
// swiper
// --------------------------

/* global ready, extend, Swiper */
ready(() => {
  'use strict';

  const elSwiper = document.querySelectorAll('[data-plugin="swiper"]');
  Array.prototype.forEach.call(elSwiper, (el) => {
    let defaults = {
      navigation: {
        nextEl: el.querySelector('.swiper-button-next'),
        prevEl: el.querySelector('.swiper-button-prev')
      },
      pagination: {
        el: el.querySelector('.swiper-pagination'),
        clickable: true
      }
    };
    let options = extend({}, defaults, JSON.parse(el.getAttribute('data-options')));

    new Swiper(el, options);
  });
});


// --------------------------
// tingle.js
// --------------------------

/* global ready, extend, tingle, ScrollTrigger */
ready(() => {
  'use strict';

  const elTingleIframe = document.querySelectorAll('[data-plugin="tingle-iframe"]');
  Array.prototype.forEach.call(elTingleIframe, (el) => {
    let defaults = {
      closeLabel: 'Đóng',
      cssClass: ['tingle-modal--iframe'],
      onClose: function(){
        this.destroy();
        ScrollTrigger.getAll().forEach(el => el.enable());
      },
      onOpen: function() {
        ScrollTrigger.getAll().forEach(el => el.disable());
      }
    };

    let options = extend({}, defaults, JSON.parse(el.getAttribute('data-options')));

    el.addEventListener('click', (e) => {
      e.preventDefault();
      let modal = new tingle.modal(options);
      modal.setContent(`<iframe src="${el.getAttribute('href')}?autoplay=1" frameborder="0" allowfullscreen></iframe>`);
      modal.open();
    });
  });


  const elTingle = document.querySelectorAll('[data-plugin="tingle"]');
  Array.prototype.forEach.call(elTingle, (el) => {
    let defaults = {
      closeLabel: 'Đóng',
      onClose: function(){
        ScrollTrigger.getAll().forEach(el => el.enable());
      },
      onOpen: function() {
        ScrollTrigger.getAll().forEach(el => el.disable());
      }
    };

    let options = extend({}, defaults, JSON.parse(el.getAttribute('data-options')));
    options.footer = options.cssClass.includes('tingle-modal--close-inside');
    let modal = new tingle.modal(options);
    modal.setContent(document.getElementById(el.dataset.target).innerHTML);

    if (options.cssClass.includes('tingle-modal--close-inside')) {
      modal.addFooterBtn('Đóng', 'tingle-close-inside', function () {
        modal.close();
      });
    }

    el.addEventListener('click', (e) => {
      e.preventDefault();
      modal.open();
    });
  });
});


// --------------------------
// swiper
// --------------------------

/* global ready, SmoothScroll */
ready(() => {
  'use strict';

  new SmoothScroll('[data-scroll]', {
    offset: window.innerWidth < 992 ? 0 : document.getElementById('header').offsetHeight
  });
});


// --------------------------
// gsap
// --------------------------

/* global ready, gsap, ScrollTrigger, imagesLoaded */
ready(() => {
  'use strict';

  gsap.registerPlugin(ScrollTrigger);

  const loaderText = document.getElementById('loader-text');
  const updateProgress = (instance) => {
    loaderText.innerHTML = `${Math.round(instance.progressedCount * 100 / instance.images.length)}%`;
  };

  const showApp = () => {
    // console.log(instance.images.length);
    // instance.images.forEach((el) => {
    //   console.log(el.img.currentSrc);
    // });

    setTimeout(() => {
      window.scrollTo(0,0);
      gsap.to('body', {overflow: 'auto'});
      gsap.to('#loader', {paddingRight: 0, duration: 0});
      gsap.to('#loader', {
        autoAlpha: 0,
        onComplete: () => {
          document.getElementById('loader').style.display = 'none';
        }
      });
    }, 600);

    const tlSection1 = gsap.timeline({
      defaults: {
        autoAlpha: 0
      },
    });

    tlSection1
      .from('#section-1 .section-logo', {y: '-2rem'})
      .from('#section-1 .section-title', {y: '-2rem'}, '-=0.2')
      .from('#download-sticky', {
        onStart: () => {
          document.getElementById('download-sticky').classList.add('active');
        }
      }, '-=0.2')
      .from('.gift', {scale: .5, ease: 'back.out'})
      .to('#section-1 .section-title', {autoAlpha: 1, scale: 1.05, yoyoEase: true, repeat: -1, duration: 0.8});

    tlSection1.delay(0.6);
  };

  imagesLoaded('body', {background: '.page, .download'}).on('progress', updateProgress).on('always', showApp);

  if (window.innerWidth >= 992) {
    ScrollTrigger.defaults({
      // toggleActions: 'play complete none reverse',
      start: 'top bottom',
      end: '80% bottom',
      scrub: 1,
      // markers: true
    });

    // section 2
    const tlSection2 = gsap.timeline({
      defaults: {autoAlpha: 0},
      scrollTrigger: {trigger: '#section-2'}
    });
    const tlSection2Position = '-=0.4';

    tlSection2
      .from('#section-2 .section-title', {y: '6rem'})
      .from('#section-2 .section-reward', {y: '6rem'}, tlSection2Position)
      .from('#section-2 .section-countdown', {y: '6rem'}, tlSection2Position)
      .from('#section-2 .section-title-2', {y: '6rem'}, tlSection2Position)
      .from('#section-2 .ranking', {y: '6rem'}, tlSection2Position);
  }
});


// --------------------------
// perfect scrollbar
// --------------------------

/* global ready, extend, PerfectScrollbar */
ready(() => {
  'use strict';

  if (window.innerWidth >= 992) {
    const elPerfectScrollbar = document.querySelectorAll('[data-plugin="perfect-scrollbar"]');
    Array.prototype.forEach.call(elPerfectScrollbar, (el) => {
      let defaults = {};
      let options = extend({}, defaults, JSON.parse(el.getAttribute('data-options')));

      new PerfectScrollbar(el, options);
    });
  }
});
