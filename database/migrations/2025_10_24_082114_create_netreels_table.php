<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNetreelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('netreels', function (Blueprint $table) {
            $table->id();
            $table->string('_id')->nullable();
            $table->string('header_language')->nullable();
            $table->longText('all_language')->nullable();
            $table->string('original_title')->nullable();
            $table->string('link')->nullable();
            $table->string('drama_cover')->nullable();
            $table->string('title')->nullable();
            $table->string('chapter_id')->nullable();
            $table->integer('episode')->nullable();
            $table->string('another_id')->nullable();
            $table->string('link_movie')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('netreels');
    }
}
