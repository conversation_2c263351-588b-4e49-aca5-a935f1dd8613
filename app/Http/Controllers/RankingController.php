<?php

namespace App\Http\Controllers;

use App\Models\Ranking;
use App\Models\Netreels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\URL;

class RankingController extends Controller
{
    public function index()
    {
        $currentUrl = URL::current();
        $keyword = "vn/";
        $keyReplace = ['vn/', '/'];
        $result = str_replace($keyReplace, "", strstr($currentUrl, $keyword));
        $data = config('app.cum_server')[$result];
        $rank = $this->getRanking($result, $data['server']);

        return view($data['view'], [
            'data' => $rank
        ]);
    }

    public function ranking(): \Illuminate\Http\JsonResponse
    {
        $rank = $this->getRanking();
        return response()->json([
            'status' => 1,
            'data' => $rank
        ]);
    }

    protected function getRanking($cum, $server_id)
    {
        return Cache::remember('ranking-' . $cum, 60 * 60, function () use ($server_id) {
            return Ranking::query()
                ->with('server:id,name')
                ->where('server_id', '>=', $server_id)
                ->orderByDesc('role_power')
                ->limit(30)
                ->get();
        });
    }
}
