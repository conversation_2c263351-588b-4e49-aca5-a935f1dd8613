<?php

namespace App\Console\Commands;

use Maatwebsite\Excel\Facades\Excel;
use App\Models\Netreels;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use JsonMachine\Items;

class UpdateLink extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:update-link';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $filename = 'movie.episode.json';
        $path = storage_path('app/' . $filename);

        if (!file_exists($path)) {
            $this->error("Không tìm thấy file: $path");
            return Command::FAILURE;
        }

        $this->info("Đang đọc file JSON lớn...");

        $items = Items::fromFile($path);
        $count = 0;
        $updated = 0;
        foreach ($items as $key => $value) {
            $count++;

            $movieId = $value->movie_id->{'$oid'} ?? null;
            $link = $value->drama_h264_url ?? null;

            if (!$movieId || !$link) {
                Log::warning("Bỏ qua item #{$count} vì thiếu movie_id hoặc link");
                continue;
            }

            $net = Netreels::where('_id',  $movieId)->first();

            if (!$net) {
                Log::warning("Không tìm thấy Netreels ID: {$movieId}");
                continue;
            }

            if (!empty($net->link_movie)) {
                continue;
            }

            $net->update(['link_movie' => $link]);
            $updated++;

            if ($count % 100 == 0) {
                $this->info("Đã xử lý {$count} dòng, cập nhật {$updated} record.");
            }
        }

        $this->info("Đã đọc xong $count phần tử.");
        return 0;
    }
}
