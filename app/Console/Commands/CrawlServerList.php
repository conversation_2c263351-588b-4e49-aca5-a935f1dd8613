<?php

namespace App\Console\Commands;

use App\Models\Netreels;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CrawlServerList extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:crawl-server-list';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $response = Http::retry(3, 5000)
            ->timeout(60)
            ->get('http://manager-t42.mobigame.vn/api/vtc/get_server_list.php');

        if ($response->ok()) {
            $servers = $response->json();
            $dataInsert = [];
            $now = now();

            if (!empty($servers) && is_array($servers)) {
                foreach ($servers as $server) {
                    if ($server['showserverid'] >= 2216) {
                        $dataInsert[] =  [
                            'id' => $server['showserverid'],
                            'name' => $server['servername'],
                            'created_at' => $now,
                            'updated_at' => $now,
                        ];
                    }
                }

                DB::beginTransaction();
                try {
                    Netreels::truncate();
                    Netreels::insert($dataInsert);
                    DB::commit();
                } catch (\Exception $exception) {
                    DB::rollBack();
                    Log::error('Lỗi cập nhật danh sách server: ' . $exception->getMessage());
                }
            }
        }

        return 0;
    }
}
