<?php

namespace App\Console\Commands;

use Maatwebsite\Excel\Facades\Excel;
use App\Models\Netreels;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CrawlNetreels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:crawl-db';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $filename = 'movie.move.xlsx';
        $path = storage_path('app/' . $filename);

        if (!file_exists($path)) {
            $this->error("❌ File không tồn tại: $path");
            return 'không có file';
        }

        $this->info("📂 Đang đọc file Excel: $path");

        try {
            $sheet = Excel::toCollection(null, $path)->first();
        } catch (\Exception $e) {
            $this->error("❌ Lỗi đọc file Excel: " . $e->getMessage());
            return 'Lỗi đọc file Excel';
        }

        if (!$sheet || $sheet->count() <= 1) {
            $this->warn("⚠️ File Excel trống hoặc không có dữ liệu!");
            return 'File Excel trống hoặc không có dữ liệu!';
        }

        $rows = $sheet->skip(1); // Bỏ qua dòng tiêu đề
        $count = 0;

        foreach ($rows as $row) {
            if (!isset($row[0])) continue; // bỏ dòng trống

            DB::table('netreels')->insert([
                '_id'             => $row[0] ?? '',
                'header_language' => $row[1] ?? '',
                'all_language'    => $row[2] ?? '',
                'original_title'  => $row[3] ?? '',
                'link'            => $row[4] ?? '',
                'drama_cover'     => $row[5] ?? '',
                'title'           => $row[6] ?? '',
                'chapter_id'      => $row[7] ?? '',
                'episode'         => $row[8] ?? '',
                'another_id'      => '',
                'created_at'      => now(),
                'updated_at'      => now(),
            ]);
        }

        $this->info("✅ Đã import thành công {$count} dòng vào bảng 'movies'.");
        return 'thành công';

    }
}
